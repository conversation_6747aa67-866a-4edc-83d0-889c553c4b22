# 命名规范文档

## 命名前缀系统

### 功能模块前缀
| 前缀 | 用途 | 示例 |
|------|------|------|
| `aisp_` | AI Side Panel 主要功能 | `aisp_initialize()` |
| `content_` | 内容相关功能 | `content_capture()` |
| `template_` | 模板系统相关 | `template_show()` |
| `lang_` | 语言相关功能 | `lang_switch()` |
| `kb_` | 知识库相关 | `kb_sync()` |
| `ui_` | 用户界面相关 | `ui_render()` |
| `api_` | API接口相关 | `api_call()` |
| `storage_` | 存储相关 | `storage_save()` |
| `util_` | 工具函数 | `util_debounce()` |

## 命名规则

### 函数命名
- 使用驼峰命名法
- 动词开头，描述功能
- 包含模块前缀
- 避免缩写，保持可读性

### 变量命名
- 使用驼峰命名法
- 名词性，描述内容
- 布尔值以is/has/can开头
- 常量使用全大写+下划线

### 文件命名
- 使用短横线分隔
- 小写字母
- 描述性名称
- 包含功能模块标识

### 避免的命名
- ❌ `unified`, `mapping`, `converter`, `helper`
- ❌ `data`, `info`, `item`, `obj`
- ❌ 单字母变量（除循环计数器）
- ❌ 数字结尾的变量名

## 新增命名记录 (性能优化阶段)

### 流式传输相关
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_autoAnalyzeCurrentPage | 函数 | 自动分析当前页面 | src/sidepanel/sidepanel.js | aisp_apiManager |
| analyzeContentStream | 函数 | 流式分析内容 | src/utils/gemini-api.js | _makeStreamRequest |
| _makeStreamRequest | 函数 | 发送流式请求 | src/utils/gemini-api.js | _sendStreamHttpRequest |
| _sendStreamHttpRequest | 函数 | 发送流式HTTP请求 | src/utils/gemini-api.js | fetch API |

### 聊天界面增强
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| addStreamMessage | 函数 | 添加流式消息 | src/components/chat-interface.js | ui_renderMessage |
| ui_typewriterEffect | 函数 | 打字机效果 | src/components/chat-interface.js | ui_scrollToBottom |
| ui_updateStreamingMessage | 函数 | 更新流式消息 | src/components/chat-interface.js | ui_typewriterEffect |

### CSS变量优化
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| --aisp-font-size-* | CSS变量 | 字体大小（缩小40%） | styles/common.css | 无 |
| --aisp-spacing-* | CSS变量 | 间距系统（减少30-50%） | styles/common.css | 无 |

## 新增命名记录 (运行时监控系统阶段)

### 增强日志系统
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_logAPIRequest | 函数 | 记录API请求开始 | src/utils/logger.js | aisp_generateRequestId |
| aisp_logAPIResponse | 函数 | 记录API响应结束 | src/utils/logger.js | aisp_sanitizeResponseData |
| aisp_logStreamChunk | 函数 | 记录流式数据块 | src/utils/logger.js | aisp_sanitizeStreamContent |
| aisp_logContentExtraction | 函数 | 记录内容提取信息 | src/utils/logger.js | aisp_logGetContext |
| aisp_logUserAction | 函数 | 记录用户交互 | src/utils/logger.js | aisp_logGetContext |
| aisp_getRuntimeData | 函数 | 获取运行时数据 | src/utils/logger.js | aispRuntimeData |
| aisp_clearRuntimeData | 函数 | 清理运行时数据 | src/utils/logger.js | aispRuntimeData |
| aisp_startRuntimeMonitoring | 函数 | 启动运行时监控 | src/utils/logger.js | aisp_startPerformanceMonitoring |
| aisp_stopRuntimeMonitoring | 函数 | 停止运行时监控 | src/utils/logger.js | clearInterval |
| aisp_syncToDevTools | 函数 | 同步数据到DevTools | src/utils/logger.js | chrome.runtime.sendMessage |

### API监控增强
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| _logAPIRequestStart | 函数 | 记录API请求开始 | src/utils/gemini-api.js | _sanitizeRequestData |
| _logAPIRequestEnd | 函数 | 记录API请求结束 | src/utils/gemini-api.js | _sanitizeResponseData |
| _logStreamChunk | 函数 | 记录流式数据块 | src/utils/gemini-api.js | aisp_logStreamChunk |
| _sanitizeRequestData | 函数 | 清理请求数据 | src/utils/gemini-api.js | JSON.stringify |
| _sanitizeResponseData | 函数 | 清理响应数据 | src/utils/gemini-api.js | JSON.stringify |
| _sanitizeStreamContent | 函数 | 清理流式内容 | src/utils/gemini-api.js | substring |
| getMonitoringData | 函数 | 获取监控数据 | src/utils/gemini-api.js | monitoring对象 |
| clearMonitoringData | 函数 | 清理监控数据 | src/utils/gemini-api.js | monitoring对象 |
| toggleMonitoring | 函数 | 切换监控状态 | src/utils/gemini-api.js | monitoring.enabled |

### 调试面板增强
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| switchTab | 函数 | 切换标签页 | src/components/debug-panel.js | refreshCurrentTab |
| refreshCurrentTab | 函数 | 刷新当前标签页 | src/components/debug-panel.js | refreshAPIDisplay |
| refreshAPIDisplay | 函数 | 刷新API显示 | src/components/debug-panel.js | aisp_getRuntimeData |
| refreshStreamDisplay | 函数 | 刷新流式显示 | src/components/debug-panel.js | aisp_getRuntimeData |
| refreshPerformanceDisplay | 函数 | 刷新性能显示 | src/components/debug-panel.js | aisp_getRuntimeData |
| refreshMemoryDisplay | 函数 | 刷新内存显示 | src/components/debug-panel.js | aisp_getRuntimeData |
| createAPICallElement | 函数 | 创建API调用元素 | src/components/debug-panel.js | createElement |
| createStreamChunkElement | 函数 | 创建流式数据块元素 | src/components/debug-panel.js | createElement |
| createPerformanceMetricElement | 函数 | 创建性能指标元素 | src/components/debug-panel.js | createElement |
| createMemoryUsageElement | 函数 | 创建内存使用元素 | src/components/debug-panel.js | createElement |
| renderPerformanceChart | 函数 | 渲染性能图表 | src/components/debug-panel.js | innerHTML |
| renderMemoryChart | 函数 | 渲染内存图表 | src/components/debug-panel.js | innerHTML |
| toggleDevMode | 函数 | 切换开发模式 | src/components/debug-panel.js | aisp_logSetConfig |
| toggleRealtimeSync | 函数 | 切换实时同步 | src/components/debug-panel.js | aisp_logSetConfig |
| searchLogs | 函数 | 搜索日志 | src/components/debug-panel.js | querySelectorAll |
| clearCurrentTabData | 函数 | 清空当前标签数据 | src/components/debug-panel.js | aisp_clearRuntimeData |
| exportCurrentTabData | 函数 | 导出当前标签数据 | src/components/debug-panel.js | aisp_getRuntimeData |

### 内容脚本监控
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_logContentExtractionStart | 函数 | 记录内容提取开始 | src/content/content-script.js | chrome.runtime.sendMessage |
| aisp_logContentExtractionEnd | 函数 | 记录内容提取结束 | src/content/content-script.js | chrome.runtime.sendMessage |
| aisp_generateExtractionId | 函数 | 生成提取ID | src/content/content-script.js | Date.now |
| aisp_logUserInteraction | 函数 | 记录用户交互 | src/content/content-script.js | chrome.runtime.sendMessage |
| aisp_getElementPath | 函数 | 获取元素路径 | src/content/content-script.js | querySelectorAll |

### 侧边栏监控
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_logSidepanelUserAction | 函数 | 记录侧边栏用户交互 | src/sidepanel/sidepanel.js | chrome.runtime.sendMessage |
| aisp_logSidepanelPerformance | 函数 | 记录侧边栏性能指标 | src/sidepanel/sidepanel.js | chrome.runtime.sendMessage |

### 后台脚本增强
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_broadcastRuntimeData | 函数 | 广播运行时数据 | src/background/service-worker.js | chrome.tabs.query |

### CSS样式增强
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| .aisp-debug-tabs | CSS类 | 调试面板标签页 | src/sidepanel/sidepanel.css | 无 |
| .aisp-debug-tab-content | CSS类 | 标签页内容 | src/sidepanel/sidepanel.css | 无 |
| .aisp-debug-data-list | CSS类 | 数据列表 | src/sidepanel/sidepanel.css | 无 |
| .aisp-debug-data-item | CSS类 | 数据项 | src/sidepanel/sidepanel.css | 无 |
| .aisp-debug-chart | CSS类 | 图表容器 | src/sidepanel/sidepanel.css | 无 |
| .aisp-debug-simple-chart | CSS类 | 简单图表 | src/sidepanel/sidepanel.css | 无 |
| .aisp-debug-chart-bar | CSS类 | 图表柱状条 | src/sidepanel/sidepanel.css | 无 |

## 新增命名记录 (代码规范化阶段)

### 命名前缀统一化 (ui_ → aisp_)
| 原名称 | 新名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|--------|--------|------|------|----------|----------|
| ui_MindMapRenderer | aisp_MindMapRenderer | 类 | 思维导图渲染器 | src/components/mindmap-renderer.js | 无 |
| ui_initializeMindMap | aisp_initializeMindMap | 函数 | 初始化思维导图 | src/components/mindmap-renderer.js | aisp_createToolbar |
| ui_createToolbar | aisp_createToolbar | 函数 | 创建工具栏 | src/components/mindmap-renderer.js | aisp_createToolbarButton |
| ui_createToolbarButton | aisp_createToolbarButton | 函数 | 创建工具栏按钮 | src/components/mindmap-renderer.js | 无 |
| ui_createGradients | aisp_createGradients | 函数 | 创建渐变定义 | src/components/mindmap-renderer.js | 无 |
| ui_setupInteractions | aisp_setupInteractions | 函数 | 设置交互功能 | src/components/mindmap-renderer.js | aisp_zoom |
| ui_zoom | aisp_zoom | 函数 | 缩放功能 | src/components/mindmap-renderer.js | aisp_updateTransform |
| ui_resetView | aisp_resetView | 函数 | 重置视图 | src/components/mindmap-renderer.js | aisp_updateTransform |
| ui_updateTransform | aisp_updateTransform | 函数 | 更新变换 | src/components/mindmap-renderer.js | 无 |
| ui_calculateNodePositions | aisp_calculateNodePositions | 函数 | 计算节点位置 | src/components/mindmap-renderer.js | aisp_organizeLevels |
| ui_organizeLevels | aisp_organizeLevels | 函数 | 组织节点层级 | src/components/mindmap-renderer.js | 无 |
| ui_renderNodes | aisp_renderNodes | 函数 | 渲染节点 | src/components/mindmap-renderer.js | aisp_renderNode |
| ui_renderNode | aisp_renderNode | 函数 | 渲染单个节点 | src/components/mindmap-renderer.js | aisp_getBranchColor |
| ui_getBranchColor | aisp_getBranchColor | 函数 | 获取分支颜色 | src/components/mindmap-renderer.js | 无 |
| ui_wrapText | aisp_wrapText | 函数 | 文本换行 | src/components/mindmap-renderer.js | 无 |
| ui_addExpandIndicator | aisp_addExpandIndicator | 函数 | 添加展开指示器 | src/components/mindmap-renderer.js | aisp_toggleNodeExpansion |
| ui_renderLinks | aisp_renderLinks | 函数 | 渲染连线 | src/components/mindmap-renderer.js | aisp_renderLink |
| ui_renderLink | aisp_renderLink | 函数 | 渲染单个连线 | src/components/mindmap-renderer.js | 无 |
| ui_handleNodeClick | aisp_handleNodeClick | 函数 | 处理节点点击 | src/components/mindmap-renderer.js | aisp_toggleNodeExpansion |
| ui_toggleNodeExpansion | aisp_toggleNodeExpansion | 函数 | 切换节点展开状态 | src/components/mindmap-renderer.js | renderMindMap |
| ui_renderEmptyState | aisp_renderEmptyState | 函数 | 渲染空状态 | src/components/mindmap-renderer.js | 无 |

### 命名前缀统一化 (po_ → aisp_)
| 原名称 | 新名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|--------|--------|------|------|----------|----------|
| po_initialize | aisp_initialize | 函数 | 初始化性能优化器 | src/utils/performance-optimizer.js | aisp_startMemoryMonitoring |
| po_optimizeApiCall | aisp_optimizeApiCall | 函数 | 优化API调用 | src/utils/performance-optimizer.js | aisp_executeWithTimeout |
| po_executeWithTimeout | aisp_executeWithTimeout | 函数 | 带超时的执行 | src/utils/performance-optimizer.js | 无 |
| po_generateRequestId | aisp_generateRequestId | 函数 | 生成请求ID | src/utils/performance-optimizer.js | aisp_simpleHash |
| po_simpleHash | aisp_simpleHash | 函数 | 简单哈希函数 | src/utils/performance-optimizer.js | 无 |
| po_delay | aisp_delay | 函数 | 延迟函数 | src/utils/performance-optimizer.js | 无 |
| po_startMemoryMonitoring | aisp_startMemoryMonitoring | 函数 | 启动内存监控 | src/utils/performance-optimizer.js | aisp_checkMemoryUsage |
| po_checkMemoryUsage | aisp_checkMemoryUsage | 函数 | 检查内存使用 | src/utils/performance-optimizer.js | aisp_performMemoryCleanup |
| po_performMemoryCleanup | aisp_performMemoryCleanup | 函数 | 执行内存清理 | src/utils/performance-optimizer.js | 无 |
| po_debounce | aisp_debounce | 函数 | 防抖函数 | src/utils/performance-optimizer.js | 无 |
| po_throttle | aisp_throttle | 函数 | 节流函数 | src/utils/performance-optimizer.js | 无 |
| po_optimizeRender | aisp_optimizeRender | 函数 | 优化渲染函数 | src/utils/performance-optimizer.js | aisp_profileFunction |
| po_profileFunction | aisp_profileFunction | 函数 | 函数性能分析 | src/utils/performance-optimizer.js | aisp_recordExecutionTime |
| po_recordExecutionTime | aisp_recordExecutionTime | 函数 | 记录执行时间 | src/utils/performance-optimizer.js | 无 |
| po_requestAnimationFrame | aisp_requestAnimationFrame | 函数 | 优化的动画帧请求 | src/utils/performance-optimizer.js | aisp_recordExecutionTime |
| po_startPerformanceMonitoring | aisp_startPerformanceMonitoring | 函数 | 启动性能监控 | src/utils/performance-optimizer.js | aisp_handlePerformanceEntry |
| po_handlePerformanceEntry | aisp_handlePerformanceEntry | 函数 | 处理性能条目 | src/utils/performance-optimizer.js | 无 |
| po_collectPerformanceMetrics | aisp_collectPerformanceMetrics | 函数 | 收集性能指标 | src/utils/performance-optimizer.js | aisp_getPerformanceMetrics |
| po_getPerformanceMetrics | aisp_getPerformanceMetrics | 函数 | 获取性能指标 | src/utils/performance-optimizer.js | 无 |
| po_formatBytes | aisp_formatBytes | 函数 | 格式化字节数 | src/utils/performance-optimizer.js | 无 |
| po_formatDuration | aisp_formatDuration | 函数 | 格式化持续时间 | src/utils/performance-optimizer.js | 无 |

### CSS类名统一化
| 原名称 | 新名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|--------|--------|------|------|----------|----------|
| .mindmap-svg-container | .aisp-mindmap-svg-container | CSS类 | 思维导图SVG容器 | src/components/mindmap-renderer.js | 无 |
| .mindmap-main | .aisp-mindmap-main | CSS类 | 思维导图主组 | src/components/mindmap-renderer.js | 无 |
| .mindmap-links | .aisp-mindmap-links | CSS类 | 思维导图连线组 | src/components/mindmap-renderer.js | 无 |
| .mindmap-nodes | .aisp-mindmap-nodes | CSS类 | 思维导图节点组 | src/components/mindmap-renderer.js | 无 |
| .mindmap-toolbar | .aisp-mindmap-toolbar | CSS类 | 思维导图工具栏 | src/components/mindmap-renderer.js | 无 |
| .mindmap-node | .aisp-mindmap-node | CSS类 | 思维导图节点 | src/components/mindmap-renderer.js | 无 |

### 新增架构组件
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_initializeExtension | 函数 | 统一初始化扩展程序 | src/utils/initialization-manager.js | aisp_groupComponentsByPriority |
| aisp_groupComponentsByPriority | 函数 | 按优先级分组组件 | src/utils/initialization-manager.js | 无 |
| aisp_initializeComponentGroup | 函数 | 初始化组件组 | src/utils/initialization-manager.js | aisp_initializeComponent |
| aisp_initializeComponent | 函数 | 初始化单个组件 | src/utils/initialization-manager.js | aisp_waitForDependencies |
| aisp_waitForDependencies | 函数 | 等待依赖组件就绪 | src/utils/initialization-manager.js | 无 |
| aisp_executeComponentInitialization | 函数 | 执行组件初始化 | src/utils/initialization-manager.js | aisp_executeWithTimeout |
| aisp_validateCriticalComponents | 函数 | 验证关键组件 | src/utils/initialization-manager.js | aisp_validateComponentFunction |
| aisp_validateComponentFunction | 函数 | 验证组件功能 | src/utils/initialization-manager.js | 无 |
| aisp_getInitializationState | 函数 | 获取初始化状态 | src/utils/initialization-manager.js | 无 |
| aisp_isComponentInitialized | 函数 | 检查组件是否已初始化 | src/utils/initialization-manager.js | 无 |
| aisp_waitForComponent | 函数 | 等待组件初始化完成 | src/utils/initialization-manager.js | 无 |
| aisp_handleError | 函数 | 统一错误处理函数 | src/utils/error-handler.js | aisp_normalizeError |
| aisp_normalizeError | 函数 | 标准化错误对象 | src/utils/error-handler.js | aisp_inferErrorType |
| aisp_inferErrorType | 函数 | 推断错误类型 | src/utils/error-handler.js | 无 |
| aisp_inferComponent | 函数 | 推断错误来源组件 | src/utils/error-handler.js | 无 |
| aisp_generateErrorId | 函数 | 生成错误ID | src/utils/error-handler.js | 无 |
| aisp_getErrorRule | 函数 | 获取错误处理规则 | src/utils/error-handler.js | 无 |
| aisp_executeErrorStrategy | 函数 | 执行错误处理策略 | src/utils/error-handler.js | aisp_executeRetryStrategy |
| aisp_executeRetryStrategy | 函数 | 执行重试策略 | src/utils/error-handler.js | 无 |
| aisp_executeFallbackStrategy | 函数 | 执行降级策略 | src/utils/error-handler.js | 无 |
| aisp_executeReloadStrategy | 函数 | 执行重新加载策略 | src/utils/error-handler.js | 无 |
| aisp_recordErrorStats | 函数 | 记录错误统计 | src/utils/error-handler.js | 无 |
| aisp_logError | 函数 | 记录错误到日志系统 | src/utils/error-handler.js | aisp_getErrorRule |
| aisp_notifyUser | 函数 | 通知用户错误信息 | src/utils/error-handler.js | 无 |
| aisp_getErrorStats | 函数 | 获取错误统计信息 | src/utils/error-handler.js | 无 |

### 光标预测输入功能
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| aisp_predictInput | 函数 | 智能预测输入 | src/components/template-popup.js | aisp_generatePredictions |
| aisp_generatePredictions | 函数 | 生成预测选项 | src/components/template-popup.js | aisp_calculatePredictionScore |
| aisp_getCommonPhrases | 函数 | 获取常用短语 | src/components/template-popup.js | 无 |
| aisp_calculatePredictionScore | 函数 | 计算预测分数 | src/components/template-popup.js | 无 |
| aisp_showPredictionHint | 函数 | 显示预测提示 | src/components/template-popup.js | aisp_showPredictionOptions |
| aisp_showPredictionOptions | 函数 | 显示预测选项 | src/components/template-popup.js | aisp_confirmPrediction |
| aisp_confirmPrediction | 函数 | 确认预测输入 | src/components/template-popup.js | aisp_addToInputHistory |
| aisp_clearPrediction | 函数 | 清除预测提示 | src/components/template-popup.js | 无 |
| aisp_addToInputHistory | 函数 | 添加到输入历史 | src/components/template-popup.js | 无 |
| aisp_cyclePredictions | 函数 | 循环切换预测选项 | src/components/template-popup.js | aisp_showPredictionHint |
