/**
 * @file test-message-fix.js - 测试消息修复的脚本
 * @description 验证chrome.runtime.sendMessage的undefined响应修复是否有效
 */

// 测试函数：模拟content script发送消息
async function testMessageHandling() {
    console.log('🧪 开始测试消息处理修复...');
    
    try {
        // 测试1: 正常的content_captured消息
        console.log('📤 测试1: 发送content_captured消息');
        const response1 = await chrome.runtime.sendMessage({
            action: 'content_captured',
            data: {
                url: 'https://test.example.com',
                title: '测试页面',
                text: '这是测试内容',
                images: [],
                timestamp: Date.now()
            }
        });
        
        if (response1) {
            console.log('✅ content_captured响应正常:', response1);
        } else {
            console.log('❌ content_captured响应为undefined - 修复生效');
        }
        
        // 测试2: get_templates消息
        console.log('📤 测试2: 发送get_templates消息');
        const response2 = await chrome.runtime.sendMessage({
            action: 'get_templates'
        });
        
        if (response2) {
            console.log('✅ get_templates响应正常:', response2);
        } else {
            console.log('❌ get_templates响应为undefined - 修复生效');
        }
        
        // 测试3: capture_error消息
        console.log('📤 测试3: 发送capture_error消息');
        const response3 = await chrome.runtime.sendMessage({
            action: 'capture_error',
            error: {
                message: '测试错误',
                stack: 'Error: 测试错误\n    at test',
                url: window.location.href,
                timestamp: Date.now()
            }
        });
        
        if (response3) {
            console.log('✅ capture_error响应正常:', response3);
        } else {
            console.log('❌ capture_error响应为undefined - 需要检查service worker');
        }
        
        console.log('🎉 消息处理测试完成');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 如果在content script环境中运行
if (typeof chrome !== 'undefined' && chrome.runtime) {
    // 延迟执行测试，确保扩展完全加载
    setTimeout(testMessageHandling, 2000);
} else {
    console.log('⚠️ 此脚本需要在Chrome扩展环境中运行');
}

// 导出测试函数供手动调用
window.testMessageHandling = testMessageHandling;

console.log('📋 测试脚本已加载。可以手动调用 testMessageHandling() 进行测试');
