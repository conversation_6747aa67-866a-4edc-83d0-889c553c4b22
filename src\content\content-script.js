/**
 * @file AI Side Panel 内容脚本
 * @description 注入到网页中的脚本，负责内容捕获、输入框检测和模板弹窗显示
 */

// #region 日志系统初始化
// 由于content script无法使用importScripts，我们需要创建简化的日志函数
let contentLogger = null;

/**
 * @function aisp_initializeContentLogger - 初始化内容脚本日志系统
 * @description 创建适用于content script的日志记录器
 */
function aisp_initializeContentLogger() {
    contentLogger = {
        error: (message, data = null) => {
            console.error(`[AISP-CONTENT] [ERROR] ${message}`, data);
            aisp_sendLogToBackground('ERROR', message, data);
        },
        warn: (message, data = null) => {
            console.warn(`[AISP-CONTENT] [WARN] ${message}`, data);
            aisp_sendLogToBackground('WARN', message, data);
        },
        info: (message, data = null) => {
            console.info(`[AISP-CONTENT] [INFO] ${message}`, data);
            aisp_sendLogToBackground('INFO', message, data);
        },
        debug: (message, data = null) => {
            console.log(`[AISP-CONTENT] [DEBUG] ${message}`, data);
            aisp_sendLogToBackground('DEBUG', message, data);
        },
        performance: (operation, duration, data = {}) => {
            const perfData = { operation, duration, ...data };
            console.log(`[AISP-CONTENT] [PERF] ${operation}: ${duration}ms`, perfData);
            aisp_sendLogToBackground('INFO', `性能监控: ${operation} 耗时 ${duration}ms`, perfData);
        },
        userAction: (action, details = {}) => {
            const actionData = { action, timestamp: Date.now(), ...details };
            console.log(`[AISP-CONTENT] [USER] ${action}`, actionData);
            aisp_sendLogToBackground('INFO', `用户操作: ${action}`, actionData);
        }
    };
}

/**
 * @function aisp_sendLogToBackground - 发送日志到后台脚本
 * @param {string} level - 日志级别
 * @param {string} message - 日志消息
 * @param {Object} data - 附加数据
 */
function aisp_sendLogToBackground(level, message, data) {
    try {
        chrome.runtime.sendMessage({
            action: 'log_message',
            level: level,
            message: message,
            data: data,
            context: 'content',
            timestamp: new Date().toISOString(),
            url: window.location.href
        }).catch(() => {
            // 忽略发送失败的错误，避免循环日志
        });
    } catch (error) {
        // 忽略发送失败的错误
    }
}

/**
 * @function aisp_logContentExtractionStart - 记录内容提取开始
 * @param {string} extractionId - 提取ID
 * @param {Object} extractionData - 提取数据
 */
function aisp_logContentExtractionStart(extractionId, extractionData) {
    try {
        // 发送到增强日志系统
        chrome.runtime.sendMessage({
            action: 'aisp_runtime_data_sync',
            data: {
                type: 'AISP_RUNTIME_DATA',
                dataType: 'content_extraction',
                timestamp: Date.now(),
                data: {
                    extractionId,
                    phase: 'start',
                    ...extractionData
                },
                context: 'content'
            }
        }).catch(() => {
            // 忽略发送失败
        });
    } catch (error) {
        // 忽略错误
    }
}

/**
 * @function aisp_logContentExtractionEnd - 记录内容提取结束
 * @param {string} extractionId - 提取ID
 * @param {Object} result - 提取结果
 */
function aisp_logContentExtractionEnd(extractionId, result) {
    try {
        // 发送到增强日志系统
        chrome.runtime.sendMessage({
            action: 'aisp_runtime_data_sync',
            data: {
                type: 'AISP_RUNTIME_DATA',
                dataType: 'content_extraction',
                timestamp: Date.now(),
                data: {
                    extractionId,
                    phase: 'end',
                    ...result
                },
                context: 'content'
            }
        }).catch(() => {
            // 忽略发送失败
        });
    } catch (error) {
        // 忽略错误
    }
}

/**
 * @function aisp_generateExtractionId - 生成提取ID
 * @returns {string} 唯一的提取ID
 */
function aisp_generateExtractionId() {
    return `extract_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * @function aisp_logUserInteraction - 记录用户交互
 * @param {string} action - 交互动作
 * @param {Object} details - 交互详情
 */
function aisp_logUserInteraction(action, details = {}) {
    try {
        const interactionData = {
            action,
            timestamp: Date.now(),
            url: window.location.href,
            context: 'content',
            ...details
        };

        // 发送到增强日志系统
        chrome.runtime.sendMessage({
            action: 'aisp_runtime_data_sync',
            data: {
                type: 'AISP_RUNTIME_DATA',
                dataType: 'user_action',
                timestamp: Date.now(),
                data: interactionData,
                context: 'content'
            }
        }).catch(() => {
            // 忽略发送失败
        });

        // 同时使用原有日志系统
        contentLogger.userAction(action, details);
    } catch (error) {
        // 忽略错误
    }
}

// 立即初始化日志系统
aisp_initializeContentLogger();
// #endregion

// #region 全局变量和配置
let aisp_contentObserver = null;
let aisp_intersectionObserver = null;
let aisp_inputFocusListener = null;
let aisp_templatePopup = null;
let aisp_isInitialized = false;
let aisp_pageStructure = null;
let aisp_contentCache = new Map();
let aisp_lastCaptureTime = 0;
let aisp_initStartTime = Date.now();

// 性能监控
let aisp_performanceMetrics = {
    captureCount: 0,
    averageTime: 0,
    lastCaptureTime: 0,
    errorCount: 0
};

// 配置常量
const AISP_CONFIG = {
    CONTENT_CAPTURE_DELAY: 1000, // 内容捕获延迟（毫秒）
    INPUT_DETECTION_DELAY: 300,  // 输入框检测延迟（毫秒）
    POPUP_SHOW_DELAY: 500,       // 弹窗显示延迟（毫秒）
    MAX_TEXT_LENGTH: 10000,      // 最大文本长度
    MAX_IMAGES: 20,              // 最大图片数量
    MIN_IMAGE_SIZE: 50,          // 最小图片尺寸
    CONTENT_SCORE_THRESHOLD: 0.3, // 内容重要性阈值

    // 高级内容提取配置
    ADVANCED_EXTRACTION: {
        ENABLE_SHADOW_DOM: true,        // 启用Shadow DOM提取
        ENABLE_IFRAME_EXTRACTION: true, // 启用iframe内容提取
        ENABLE_CANVAS_OCR: true,        // 启用Canvas文字识别
        ENABLE_SVG_TEXT: true,          // 启用SVG文字提取
        ENABLE_DYNAMIC_CONTENT: true,   // 启用动态内容检测
        ENABLE_SPA_ROUTING: true,       // 启用SPA路由检测

        // 反爬虫对策
        ANTI_DETECTION: {
            RANDOMIZE_TIMING: true,     // 随机化时间间隔
            SIMULATE_HUMAN_BEHAVIOR: true, // 模拟人类行为
            RESPECT_ROBOTS_TXT: true,   // 遵守robots.txt
            RATE_LIMITING: true,        // 启用频率限制
            USER_AGENT_ROTATION: false  // 用户代理轮换（Chrome扩展中不适用）
        },

        // 性能优化
        PERFORMANCE: {
            MAX_RETRY_ATTEMPTS: 3,      // 最大重试次数
            RETRY_DELAY_BASE: 1000,     // 重试延迟基数（毫秒）
            TIMEOUT_THRESHOLD: 30000,   // 超时阈值（毫秒）
            BATCH_SIZE: 10,             // 批处理大小
            CACHE_TTL: 300000,          // 缓存生存时间（5分钟）
            QUALITY_THRESHOLD: 0.7      // 内容质量阈值
        }
    }
};

// 内容识别选择器
const CONTENT_SELECTORS = {
    // 主要内容区域
    main: [
        'article', 'main', '[role="main"]', '.content', '.post', '.article',
        '.entry', '.story', '.news-article', '.blog-post', '.page-content',
        '.main-content', '.primary-content', '.article-content'
    ],
    // 需要排除的区域
    exclude: [
        'nav', 'aside', 'footer', 'header', '.sidebar', '.menu', '.navigation',
        '.ad', '.advertisement', '.banner', '.popup', '.modal', '.overlay',
        '.social-share', '.related-posts', '.comments-section', '.breadcrumb',
        '.cookie-notice', '.newsletter', '.subscription', '.promo'
    ],
    // 富文本编辑器
    richEditors: [
        '.tox-tinymce', '.cke_editable', '.ql-editor', '.fr-element',
        '.note-editable', '.wysiwyg', '.rich-text', '.editor-content',
        '.medium-editor', '.summernote', '.trumbowyg-editor'
    ],
    // 结构化内容
    structured: {
        headings: 'h1, h2, h3, h4, h5, h6',
        lists: 'ul, ol, dl',
        tables: 'table',
        code: 'pre, code, .highlight, .code-block',
        quotes: 'blockquote, .quote'
    }
};

// 内容类型检测规则
const CONTENT_TYPE_RULES = {
    article: {
        selectors: ['article', '.post', '.blog-post', '.news-article'],
        indicators: ['h1', 'h2', 'p', '.author', '.date', '.publish-date', '.byline'],
        weight: 1.0
    },
    product: {
        selectors: ['.product', '.item', '.listing', '.product-detail'],
        indicators: ['.price', '.rating', '.reviews', '.add-to-cart', '.buy-now', '.product-title'],
        weight: 0.9
    },
    social: {
        selectors: ['.post', '.tweet', '.status', '.update', '.feed-item'],
        indicators: ['.like', '.share', '.comment', '.follow', '.user-avatar', '.timestamp'],
        weight: 0.8
    },
    form: {
        selectors: ['form', '.form', '.contact-form', '.signup-form'],
        indicators: ['input', 'textarea', 'select', 'button[type="submit"]', '.form-field'],
        weight: 0.7
    },
    documentation: {
        selectors: ['.docs', '.documentation', '.manual', '.guide'],
        indicators: ['.toc', '.table-of-contents', 'pre', 'code', '.example'],
        weight: 0.9
    }
};
// #endregion

// #region 初始化函数
/**
 * @function aisp_initializeContentScript - 初始化内容脚本
 * @description 设置内容监听器和输入框检测
 */
async function aisp_initializeContentScript() {
    if (aisp_isInitialized) return;

    try {
        contentLogger.info('AI Side Panel Content Script 开始初始化', {
            url: window.location.href,
            title: document.title
        });

        // 初始化页面结构分析
        await contentLogger.performance('页面结构分析', await aisp_measureTime(aisp_analyzePageStructure));

        // 初始化内容捕获
        await contentLogger.performance('内容捕获设置', await aisp_measureTime(aisp_setupContentCapture));

        // 初始化输入框检测
        await contentLogger.performance('输入框检测设置', await aisp_measureTime(aisp_setupInputDetection));

        // 初始化模板弹窗系统
        await contentLogger.performance('模板弹窗设置', await aisp_measureTime(aisp_setupTemplatePopup));

        // 初始化性能监控
        await contentLogger.performance('性能监控设置', await aisp_measureTime(aisp_setupPerformanceMonitoring));

        // 初始化错误处理
        await contentLogger.performance('错误处理设置', await aisp_measureTime(aisp_setupErrorHandling));

        // 初始化高级内容提取功能
        await contentLogger.performance('高级内容提取设置', await aisp_measureTime(aisp_initializeAdvancedExtraction));

        aisp_isInitialized = true;
        contentLogger.info('AI Side Panel Content Script 初始化完成', {
            initializationTime: Date.now() - aisp_initStartTime,
            advancedExtractionEnabled: true
        });
    } catch (error) {
        contentLogger.error('AI Side Panel Content Script 初始化失败', {
            error: error.message,
            stack: error.stack
        });
        aisp_handleInitializationError(error);
    }
}

/**
 * @function aisp_measureTime - 测量函数执行时间
 * @param {Function} fn - 要测量的函数
 * @returns {Promise<number>} 执行时间（毫秒）
 */
async function aisp_measureTime(fn) {
    const startTime = performance.now();
    try {
        if (typeof fn === 'function') {
            await fn();
        }
        return performance.now() - startTime;
    } catch (error) {
        contentLogger.error('函数执行失败', { error: error.message });
        return performance.now() - startTime;
    }
}

/**
 * @function aisp_setupContentCapture - 设置内容捕获
 * @description 监听页面内容变化并捕获相关信息
 */
function aisp_setupContentCapture() {
    // 检查页面是否完全加载完成
    if (document.readyState === 'complete') {
        // 页面已完全加载，立即捕获内容
        contentLogger.info('页面已完全加载，开始捕获内容');
        aisp_capturePageContent();
    } else {
        // 页面还在加载中，等待load事件
        contentLogger.info('页面正在加载，等待完全加载完成');
        window.addEventListener('load', () => {
            contentLogger.info('页面加载完成，开始捕获内容');
            aisp_capturePageContent();
        }, { once: true }); // 只执行一次
    }

    // 设置智能内容变化监听器（延迟启动，避免加载期间的干扰）
    const setupObserver = () => {
        aisp_contentObserver = new MutationObserver(
            util_debounce(aisp_handleContentChange, AISP_CONFIG.CONTENT_CAPTURE_DELAY)
        );

        aisp_contentObserver.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true,
            attributes: true,
            attributeFilter: ['class', 'id', 'style']
        });

        contentLogger.debug('内容变化监听器已启动');
    };

    // 在页面完全加载后再启动监听器
    if (document.readyState === 'complete') {
        // 延迟启动监听器，避免初始化期间的噪音
        setTimeout(setupObserver, 2000);
    } else {
        window.addEventListener('load', () => {
            setTimeout(setupObserver, 2000);
        }, { once: true });
    }

    // 设置可见区域监听器
    aisp_setupIntersectionObserver();

    // 监听页面滚动和窗口大小变化（延迟启动）
    const setupViewportListeners = () => {
        window.addEventListener('scroll', util_debounce(aisp_handleViewportChange, 300));
        window.addEventListener('resize', util_debounce(aisp_handleViewportChange, 300));
        contentLogger.debug('视口变化监听器已启动');
    };

    if (document.readyState === 'complete') {
        setupViewportListeners();
    } else {
        window.addEventListener('load', setupViewportListeners, { once: true });
    }
}

/**
 * @function aisp_setupInputDetection - 设置输入框检测
 * @description 监听输入框焦点事件，用于显示模板弹窗
 */
function aisp_setupInputDetection() {
    aisp_inputFocusListener = util_debounce(aisp_handleInputFocus, AISP_CONFIG.INPUT_DETECTION_DELAY);
    
    // 监听所有输入框的焦点事件
    document.addEventListener('focusin', (event) => {
        if (aisp_isInputElement(event.target)) {
            aisp_inputFocusListener(event.target);
        }
    });
    
    // 监听失去焦点事件
    document.addEventListener('focusout', (event) => {
        if (aisp_isInputElement(event.target)) {
            setTimeout(() => aisp_hideTemplatePopup(), 200);
        }
    });
}

/**
 * @function aisp_setupTemplatePopup - 设置模板弹窗系统
 * @description 初始化模板弹窗的DOM结构
 */
function aisp_setupTemplatePopup() {
    // 创建模板弹窗容器
    aisp_templatePopup = document.createElement('div');
    aisp_templatePopup.id = 'aisp-template-popup';
    aisp_templatePopup.className = 'aisp-template-popup';
    aisp_templatePopup.style.display = 'none';
    
    document.body.appendChild(aisp_templatePopup);
}
// #endregion

// #region 内容捕获功能
/**
 * @function aisp_capturePageContent - 捕获页面内容
 * @description 提取页面的文字和图片内容，包含智能分析和增强监控
 */
async function aisp_capturePageContent() {
    const startTime = performance.now();
    const extractionId = aisp_generateExtractionId();

    try {
        // 记录内容提取开始
        aisp_logContentExtractionStart(extractionId, {
            url: window.location.href,
            title: document.title,
            extractionType: 'full_page'
        });

        contentLogger.debug('开始捕获页面内容', {
            url: window.location.href,
            title: document.title,
            extractionId
        });

        // 检查是否需要重新捕获
        if (!aisp_shouldRecapture()) {
            contentLogger.debug('跳过内容捕获，内容未发生变化');
            aisp_logContentExtractionEnd(extractionId, {
                success: false,
                reason: 'no_changes_detected',
                duration: performance.now() - startTime
            });
            return;
        }

        // 识别主要内容区域
        const mainContentStartTime = performance.now();
        const mainContent = aisp_identifyMainContent();
        const mainContentDuration = performance.now() - mainContentStartTime;

        contentLogger.debug('主要内容识别完成', {
            textLength: mainContent?.text?.length || 0,
            duration: mainContentDuration
        });

        // 提取结构化内容
        const structuredContentStartTime = performance.now();
        const structuredContent = aisp_extractStructuredContent();
        const structuredContentDuration = performance.now() - structuredContentStartTime;

        contentLogger.debug('结构化内容提取完成', {
            headingsCount: structuredContent?.headings?.length || 0,
            duration: structuredContentDuration
        });

        // 检测内容类型
        const contentTypeStartTime = performance.now();
        const contentType = aisp_detectContentType();
        const contentTypeDuration = performance.now() - contentTypeStartTime;

        contentLogger.debug('内容类型检测完成', {
            contentType,
            duration: contentTypeDuration
        });

        // 提取元数据
        const metadataStartTime = performance.now();
        const metadata = aisp_extractMetadata();
        const metadataDuration = performance.now() - metadataStartTime;

        // 执行高级内容提取
        let advancedContent = null;
        let advancedContentDuration = 0;
        try {
            const advancedStartTime = performance.now();
            advancedContent = await aisp_performAdvancedExtraction();
            advancedContentDuration = performance.now() - advancedStartTime;

            contentLogger.debug('高级内容提取完成', {
                quality: advancedContent?.quality || 0,
                hasAdvancedContent: !!advancedContent,
                duration: advancedContentDuration
            });
        } catch (error) {
            advancedContentDuration = performance.now() - advancedStartTime;
            contentLogger.warn('高级内容提取失败，使用标准提取', {
                error: error.message,
                duration: advancedContentDuration
            });
        }

        // 提取基础内容
        const textStartTime = performance.now();
        const text = aisp_extractText();
        const textDuration = performance.now() - textStartTime;

        const imagesStartTime = performance.now();
        const images = aisp_extractImages();
        const imagesDuration = performance.now() - imagesStartTime;

        const contentData = {
            url: window.location.href,
            title: document.title,
            contentType: contentType,
            mainContent: mainContent,
            structuredContent: structuredContent,
            metadata: metadata,
            text: text,
            images: images,
            pageStructure: aisp_pageStructure,
            advancedContent: advancedContent,
            timestamp: Date.now(),
            captureId: aisp_generateCaptureId(),
            extractionId: extractionId,
            // 性能指标
            performanceMetrics: {
                mainContentDuration,
                structuredContentDuration,
                contentTypeDuration,
                metadataDuration,
                advancedContentDuration,
                textDuration,
                imagesDuration
            }
        };

        // 缓存内容数据
        aisp_cacheContentData(contentData);

        contentLogger.info('内容数据准备完成', {
            textLength: contentData.text?.length || 0,
            imagesCount: contentData.images?.length || 0,
            contentType: contentData.contentType,
            extractionId
        });

        // 发送内容到后台脚本
        const response = await chrome.runtime.sendMessage({
            action: 'content_captured',
            data: contentData
        });

        const totalDuration = performance.now() - startTime;

        // 检查响应是否有效
        if (!response) {
            contentLogger.error('后台脚本无响应', {
                extractionId,
                duration: totalDuration
            });
            aisp_performanceMetrics.errorCount++;

            // 记录内容提取失败
            aisp_logContentExtractionEnd(extractionId, {
                success: false,
                error: '后台脚本无响应',
                duration: totalDuration,
                contentLength: contentData.text?.length || 0,
                elementsCount: document.querySelectorAll('*').length
            });
            return;
        }

        if (!response.success) {
            contentLogger.error('内容捕获失败', {
                error: response.error,
                extractionId
            });
            aisp_performanceMetrics.errorCount++;

            // 记录内容提取失败
            aisp_logContentExtractionEnd(extractionId, {
                success: false,
                error: response.error,
                duration: totalDuration,
                contentLength: contentData.text?.length || 0,
                elementsCount: document.querySelectorAll('*').length
            });
        } else {
            contentLogger.info('内容捕获成功发送到后台', { extractionId });

            // 记录内容提取成功
            aisp_logContentExtractionEnd(extractionId, {
                success: true,
                duration: totalDuration,
                contentLength: contentData.text?.length || 0,
                elementsCount: document.querySelectorAll('*').length,
                imagesCount: contentData.images?.length || 0,
                contentType: contentData.contentType
            });
        }

        // 更新性能指标
        aisp_updatePerformanceMetrics(startTime);
        contentLogger.performance('页面内容捕获', totalDuration, {
            success: response.success,
            dataSize: JSON.stringify(contentData).length,
            extractionId
        });

    } catch (error) {
        const duration = performance.now() - startTime;
        contentLogger.error('捕获页面内容时出错', {
            error: error.message,
            stack: error.stack,
            duration,
            extractionId
        });
        aisp_performanceMetrics.errorCount++;

        // 记录内容提取错误
        aisp_logContentExtractionEnd(extractionId, {
            success: false,
            error: error.message,
            duration: duration,
            contentLength: 0,
            elementsCount: document.querySelectorAll('*').length
        });

        aisp_handleCaptureError(error);
    }
}

/**
 * @function content_extractText - 提取页面文字内容
 * @returns {string} 提取的文字内容
 */
/**
 * @function aisp_extractText - 提取页面文字内容（增强版）
 * @returns {string} 提取的文字内容
 */
function aisp_extractText() {
    // 优先从主内容区域提取
    const mainContent = aisp_identifyMainContent();
    if (mainContent && mainContent.text) {
        return mainContent.text.length > AISP_CONFIG.MAX_TEXT_LENGTH ?
               mainContent.text.substring(0, AISP_CONFIG.MAX_TEXT_LENGTH) + '...' :
               mainContent.text;
    }

    // 回退到传统方法
    const excludeSelectors = 'script, style, noscript, iframe, .aisp-template-popup, nav, header, footer, .advertisement, .ad, .sidebar, .menu';
    const excludeElements = document.querySelectorAll(excludeSelectors);

    // 临时隐藏排除的元素
    const hiddenElements = [];
    excludeElements.forEach(el => {
        if (el.style.display !== 'none') {
            hiddenElements.push({
                element: el,
                originalDisplay: el.style.display
            });
            el.style.display = 'none';
        }
    });

    // 提取可见文本
    const textContent = document.body.innerText || document.body.textContent || '';

    // 恢复隐藏的元素
    hiddenElements.forEach(item => {
        item.element.style.display = item.originalDisplay;
    });

    // 清理和格式化文本
    let text = textContent
        .replace(/\s+/g, ' ')           // 合并多个空白字符
        .replace(/\n\s*\n/g, '\n')      // 合并多个换行
        .trim();                        // 去除首尾空白

    // 限制长度
    if (text.length > AISP_CONFIG.MAX_TEXT_LENGTH) {
        text = text.substring(0, AISP_CONFIG.MAX_TEXT_LENGTH) + '...';
    }

    return text;
}

/**
 * @function content_extractText - 保持向后兼容的文本提取函数
 * @returns {string} 提取的文字内容
 */
function content_extractText() {
    return aisp_extractText();
}

/**
 * @function aisp_extractImages - 提取页面图片信息（增强版）
 * @returns {Array} 图片信息数组
 */
function aisp_extractImages() {
    const images = [];
    const imgElements = document.querySelectorAll('img');

    imgElements.forEach((img, index) => {
        if (aisp_isValidImageElement(img)) {
            images.push({
                src: img.src,
                alt: img.alt || '',
                width: img.offsetWidth || img.naturalWidth,
                height: img.offsetHeight || img.naturalHeight,
                title: img.title || '',
                loading: img.loading || '',
                index: index,
                importance: aisp_calculateImageImportance(img)
            });
        }
    });

    // 按重要性排序并限制数量
    return images
        .sort((a, b) => b.importance - a.importance)
        .slice(0, AISP_CONFIG.MAX_IMAGES);
}

/**
 * @function content_extractImages - 保持向后兼容的图片提取函数
 * @returns {Array} 图片信息数组
 */
function content_extractImages() {
    return aisp_extractImages();
}
// #endregion

// #region 输入框检测和模板功能
/**
 * @function aisp_isInputElement - 检查元素是否为输入框
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否为输入框
 */
function aisp_isInputElement(element) {
    const inputTypes = ['input', 'textarea'];
    const editableTypes = ['text', 'email', 'search', 'url', 'tel'];
    
    if (inputTypes.includes(element.tagName.toLowerCase())) {
        if (element.tagName.toLowerCase() === 'input') {
            return editableTypes.includes(element.type) || !element.type;
        }
        return true;
    }
    
    return element.contentEditable === 'true';
}

/**
 * @function aisp_handleInputFocus - 处理输入框焦点事件
 * @param {Element} inputElement - 获得焦点的输入框元素
 */
async function aisp_handleInputFocus(inputElement) {
    try {
        // 记录用户交互（使用增强监控）
        aisp_logUserInteraction('input_focus', {
            tagName: inputElement.tagName,
            type: inputElement.type,
            id: inputElement.id,
            className: inputElement.className,
            placeholder: inputElement.placeholder,
            elementPath: aisp_getElementPath(inputElement),
            viewport: {
                x: inputElement.getBoundingClientRect().x,
                y: inputElement.getBoundingClientRect().y,
                width: inputElement.getBoundingClientRect().width,
                height: inputElement.getBoundingClientRect().height
            }
        });

        // 获取模板数据
        const response = await chrome.runtime.sendMessage({
            action: 'get_templates'
        });

        // 检查响应是否有效
        if (!response) {
            contentLogger.debug('获取模板时后台脚本无响应');
            return;
        }

        if (response.success && response.data.length > 0) {
            contentLogger.info('显示模板弹窗', {
                templatesCount: response.data.length
            });
            aisp_showTemplatePopup(inputElement, response.data);

            // 记录模板弹窗显示
            aisp_logUserInteraction('template_popup_shown', {
                inputElement: inputElement.tagName,
                inputId: inputElement.id,
                templatesCount: response.data.length
            });
        } else {
            contentLogger.debug('无可用模板或获取失败', {
                success: response.success,
                error: response.error
            });
        }
    } catch (error) {
        contentLogger.error('处理输入框焦点事件失败', {
            error: error.message,
            stack: error.stack
        });
    }
}

/**
 * @function aisp_showTemplatePopup - 显示模板弹窗
 * @param {Element} inputElement - 目标输入框
 * @param {Array} templates - 模板数据
 */
function aisp_showTemplatePopup(inputElement, templates) {
    if (!aisp_templatePopup) return;
    
    // 计算弹窗位置
    const rect = inputElement.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
    
    // 设置弹窗位置
    aisp_templatePopup.style.position = 'absolute';
    aisp_templatePopup.style.top = (rect.top + scrollTop - 100) + 'px';
    aisp_templatePopup.style.left = (rect.left + scrollLeft) + 'px';
    aisp_templatePopup.style.zIndex = '10000';
    
    // 生成模板列表HTML
    const templateHTML = templates.map((template, index) => `
        <div class="aisp-template-item" data-template-id="${template.id}" data-index="${index}">
            <div class="aisp-template-title">${template.title || '未命名模板'}</div>
            <div class="aisp-template-preview">${(template.content || '').substring(0, 50)}...</div>
        </div>
    `).join('');
    
    aisp_templatePopup.innerHTML = `
        <div class="aisp-template-header">快捷回复模板</div>
        <div class="aisp-template-list">${templateHTML}</div>
        <div class="aisp-template-footer">
            <small>点击选择模板，按Tab键插入</small>
        </div>
    `;
    
    // 添加点击事件监听
    aisp_templatePopup.addEventListener('click', (event) => {
        const templateItem = event.target.closest('.aisp-template-item');
        if (templateItem) {
            const templateId = templateItem.dataset.templateId;
            const template = templates.find(t => t.id === templateId);
            if (template) {
                aisp_insertTemplate(inputElement, template.content);
                aisp_hideTemplatePopup();
            }
        }
    });
    
    // 显示弹窗
    aisp_templatePopup.style.display = 'block';
}

/**
 * @function aisp_hideTemplatePopup - 隐藏模板弹窗
 */
function aisp_hideTemplatePopup() {
    if (aisp_templatePopup) {
        aisp_templatePopup.style.display = 'none';
    }
}

/**
 * @function aisp_insertTemplate - 插入模板内容到输入框
 * @param {Element} inputElement - 目标输入框
 * @param {string} content - 要插入的内容
 */
function aisp_insertTemplate(inputElement, content) {
    if (inputElement.tagName.toLowerCase() === 'input' || inputElement.tagName.toLowerCase() === 'textarea') {
        inputElement.value = content;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
    } else if (inputElement.contentEditable === 'true') {
        inputElement.textContent = content;
        inputElement.dispatchEvent(new Event('input', { bubbles: true }));
    }

    // 记录模板使用
    aisp_logUserInteraction('template_used', {
        inputElement: inputElement.tagName,
        inputId: inputElement.id,
        contentLength: content.length
    });

    // 设置焦点到输入框末尾
    inputElement.focus();
    if (inputElement.setSelectionRange) {
        inputElement.setSelectionRange(content.length, content.length);
    }
}

/**
 * @function aisp_getElementPath - 获取元素的CSS路径
 * @param {Element} element - 目标元素
 * @returns {string} CSS选择器路径
 */
function aisp_getElementPath(element) {
    if (!element) return '';

    const path = [];
    let current = element;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
        let selector = current.nodeName.toLowerCase();

        if (current.id) {
            selector += `#${current.id}`;
            path.unshift(selector);
            break;
        } else if (current.className) {
            const classes = current.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                selector += `.${classes.join('.')}`;
            }
        }

        // 添加nth-child选择器以确保唯一性
        const siblings = Array.from(current.parentNode?.children || []);
        const index = siblings.indexOf(current);
        if (index > 0) {
            selector += `:nth-child(${index + 1})`;
        }

        path.unshift(selector);
        current = current.parentNode;

        // 限制路径深度
        if (path.length >= 5) break;
    }

    return path.join(' > ');
}
// #endregion

// #region 工具函数
/**
 * @function util_debounce - 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function util_debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
// #endregion

// #region 脚本启动
// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', aisp_initializeContentScript);
} else {
    aisp_initializeContentScript();
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (aisp_contentObserver) {
        aisp_contentObserver.disconnect();
    }
    if (aisp_templatePopup) {
        aisp_templatePopup.remove();
    }
});

// #region 智能内容分析功能

/**
 * @function aisp_analyzePageStructure - 分析页面结构
 * @description 分析页面的整体结构和布局
 */
function aisp_analyzePageStructure() {
    aisp_pageStructure = {
        hasHeader: !!document.querySelector('header, .header, #header'),
        hasNavigation: !!document.querySelector('nav, .nav, .navigation'),
        hasMain: !!document.querySelector('main, .main, .content'),
        hasSidebar: !!document.querySelector('aside, .sidebar, .side'),
        hasFooter: !!document.querySelector('footer, .footer, #footer'),
        contentAreas: aisp_identifyContentAreas(),
        pageType: aisp_guessPageType(),
        language: aisp_detectLanguage(),
        readingTime: aisp_estimateReadingTime()
    };
}

/**
 * @function aisp_identifyMainContent - 识别主要内容区域
 * @description 使用启发式算法识别页面的主要内容
 * @returns {Object} 主要内容数据
 */
function aisp_identifyMainContent() {
    const candidates = [];

    // 尝试常见的主内容选择器
    for (const selector of CONTENT_SELECTORS.main) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (aisp_isValidContentElement(element)) {
                candidates.push({
                    element: element,
                    score: aisp_calculateContentScore(element),
                    selector: selector
                });
            }
        });
    }

    // 如果没有找到明确的主内容，分析所有可能的容器
    if (candidates.length === 0) {
        const containers = document.querySelectorAll('div, section, article');
        containers.forEach(element => {
            const score = aisp_calculateContentScore(element);
            if (score > AISP_CONFIG.CONTENT_SCORE_THRESHOLD) {
                candidates.push({
                    element: element,
                    score: score,
                    selector: 'auto-detected'
                });
            }
        });
    }

    // 选择得分最高的元素
    candidates.sort((a, b) => b.score - a.score);
    const bestCandidate = candidates[0];

    if (bestCandidate) {
        return {
            text: aisp_extractCleanText(bestCandidate.element),
            html: bestCandidate.element.innerHTML,
            score: bestCandidate.score,
            selector: bestCandidate.selector,
            wordCount: aisp_countWords(bestCandidate.element),
            readingTime: aisp_estimateReadingTime(bestCandidate.element)
        };
    }

    return null;
}

/**
 * @function aisp_calculateContentScore - 计算内容重要性分数
 * @description 基于多个因素计算元素的内容重要性
 * @param {Element} element - 要评分的元素
 * @returns {number} 内容分数 (0-1)
 */
function aisp_calculateContentScore(element) {
    if (!element || aisp_isExcludedElement(element)) {
        return 0;
    }

    let score = 0;
    const text = aisp_extractCleanText(element);
    const wordCount = aisp_countWords(element);

    // 文本长度分数 (0-0.4)
    if (wordCount > 100) score += 0.4;
    else if (wordCount > 50) score += 0.3;
    else if (wordCount > 20) score += 0.2;
    else if (wordCount > 5) score += 0.1;

    // 段落密度分数 (0-0.2)
    const paragraphs = element.querySelectorAll('p');
    if (paragraphs.length > 3) score += 0.2;
    else if (paragraphs.length > 1) score += 0.1;

    // 标题存在分数 (0-0.2)
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    if (headings.length > 0) score += 0.2;

    // 链接密度惩罚 (最多-0.3)
    const links = element.querySelectorAll('a');
    const linkDensity = links.length / Math.max(wordCount / 10, 1);
    if (linkDensity > 0.5) score -= 0.3;
    else if (linkDensity > 0.3) score -= 0.2;
    else if (linkDensity > 0.1) score -= 0.1;

    // 类名和ID加分
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    if (className.includes('content') || className.includes('main') ||
        className.includes('article') || className.includes('post')) {
        score += 0.1;
    }
    if (id.includes('content') || id.includes('main') ||
        id.includes('article') || id.includes('post')) {
        score += 0.1;
    }

    return Math.max(0, Math.min(1, score));
}

/**
 * @function aisp_extractStructuredContent - 提取结构化内容
 * @description 提取页面中的结构化数据
 * @returns {Object} 结构化内容数据
 */
function aisp_extractStructuredContent() {
    return {
        headings: aisp_extractHeadings(),
        lists: aisp_extractLists(),
        tables: aisp_extractTables(),
        codeBlocks: aisp_extractCodeBlocks(),
        quotes: aisp_extractQuotes(),
        links: aisp_extractLinks(),
        forms: aisp_extractForms()
    };
}

/**
 * @function aisp_detectContentType - 检测内容类型
 * @description 基于页面特征检测内容类型
 * @returns {string} 内容类型
 */
function aisp_detectContentType() {
    const scores = {};

    // 计算每种内容类型的匹配分数
    for (const [type, rules] of Object.entries(CONTENT_TYPE_RULES)) {
        let score = 0;

        // 检查选择器匹配
        for (const selector of rules.selectors) {
            if (document.querySelector(selector)) {
                score += 0.3;
            }
        }

        // 检查指示器匹配
        for (const indicator of rules.indicators) {
            const elements = document.querySelectorAll(indicator);
            if (elements.length > 0) {
                score += 0.1 * Math.min(elements.length, 5);
            }
        }

        scores[type] = score * rules.weight;
    }

    // 返回得分最高的类型
    const sortedTypes = Object.entries(scores).sort((a, b) => b[1] - a[1]);
    return sortedTypes[0] ? sortedTypes[0][0] : 'unknown';
}

/**
 * @function aisp_extractMetadata - 提取页面元数据
 * @description 提取页面的元数据信息
 * @returns {Object} 元数据对象
 */
function aisp_extractMetadata() {
    const metadata = {
        title: document.title,
        description: '',
        keywords: '',
        author: '',
        publishDate: '',
        modifiedDate: '',
        canonicalUrl: '',
        ogData: {},
        twitterData: {},
        jsonLd: []
    };

    // 提取meta标签信息
    const metaTags = document.querySelectorAll('meta');
    metaTags.forEach(meta => {
        const name = meta.getAttribute('name') || meta.getAttribute('property');
        const content = meta.getAttribute('content');

        if (name && content) {
            switch (name.toLowerCase()) {
                case 'description':
                    metadata.description = content;
                    break;
                case 'keywords':
                    metadata.keywords = content;
                    break;
                case 'author':
                    metadata.author = content;
                    break;
                case 'article:published_time':
                case 'publish_date':
                    metadata.publishDate = content;
                    break;
                case 'article:modified_time':
                case 'modified_date':
                    metadata.modifiedDate = content;
                    break;
                default:
                    if (name.startsWith('og:')) {
                        metadata.ogData[name] = content;
                    } else if (name.startsWith('twitter:')) {
                        metadata.twitterData[name] = content;
                    }
            }
        }
    });

    // 提取canonical URL
    const canonical = document.querySelector('link[rel="canonical"]');
    if (canonical) {
        metadata.canonicalUrl = canonical.href;
    }

    // 提取JSON-LD结构化数据
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    jsonLdScripts.forEach(script => {
        try {
            const data = JSON.parse(script.textContent);
            metadata.jsonLd.push(data);
        } catch (e) {
            // 忽略解析错误
        }
    });

    return metadata;
}

/**
 * @function aisp_extractHeadings - 提取标题层次
 * @description 提取页面的标题结构
 * @returns {Array} 标题数组
 */
function aisp_extractHeadings() {
    const headings = [];
    const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');

    headingElements.forEach((heading, index) => {
        if (aisp_isValidContentElement(heading)) {
            headings.push({
                level: parseInt(heading.tagName.charAt(1)),
                text: aisp_extractCleanText(heading),
                id: heading.id || `heading-${index}`,
                position: index
            });
        }
    });

    return headings;
}

/**
 * @function aisp_extractLists - 提取列表内容
 * @description 提取页面中的列表结构
 * @returns {Array} 列表数组
 */
function aisp_extractLists() {
    const lists = [];
    const listElements = document.querySelectorAll('ul, ol, dl');

    listElements.forEach((list, index) => {
        if (aisp_isValidContentElement(list)) {
            const items = [];
            const itemElements = list.querySelectorAll('li, dt, dd');

            itemElements.forEach(item => {
                items.push({
                    text: aisp_extractCleanText(item),
                    type: item.tagName.toLowerCase()
                });
            });

            lists.push({
                type: list.tagName.toLowerCase(),
                items: items,
                id: list.id || `list-${index}`
            });
        }
    });

    return lists;
}

/**
 * @function aisp_extractTables - 提取表格数据
 * @description 提取页面中的表格内容
 * @returns {Array} 表格数组
 */
function aisp_extractTables() {
    const tables = [];
    const tableElements = document.querySelectorAll('table');

    tableElements.forEach((table, index) => {
        if (aisp_isValidContentElement(table)) {
            const headers = [];
            const rows = [];

            // 提取表头
            const headerCells = table.querySelectorAll('th');
            headerCells.forEach(cell => {
                headers.push(aisp_extractCleanText(cell));
            });

            // 提取数据行
            const dataRows = table.querySelectorAll('tbody tr, tr');
            dataRows.forEach(row => {
                const cells = [];
                const cellElements = row.querySelectorAll('td, th');
                cellElements.forEach(cell => {
                    cells.push(aisp_extractCleanText(cell));
                });
                if (cells.length > 0) {
                    rows.push(cells);
                }
            });

            tables.push({
                headers: headers,
                rows: rows,
                id: table.id || `table-${index}`,
                caption: table.caption ? aisp_extractCleanText(table.caption) : ''
            });
        }
    });

    return tables;
}

/**
 * @function aisp_extractCodeBlocks - 提取代码块
 * @description 提取页面中的代码内容
 * @returns {Array} 代码块数组
 */
function aisp_extractCodeBlocks() {
    const codeBlocks = [];
    const codeElements = document.querySelectorAll('pre, code, .highlight, .code-block');

    codeElements.forEach((element, index) => {
        if (aisp_isValidContentElement(element)) {
            codeBlocks.push({
                text: element.textContent.trim(),
                language: aisp_detectCodeLanguage(element),
                id: element.id || `code-${index}`,
                type: element.tagName.toLowerCase()
            });
        }
    });

    return codeBlocks;
}

/**
 * @function aisp_extractQuotes - 提取引用内容
 * @description 提取页面中的引用块
 * @returns {Array} 引用数组
 */
function aisp_extractQuotes() {
    const quotes = [];
    const quoteElements = document.querySelectorAll('blockquote, .quote');

    quoteElements.forEach((element, index) => {
        if (aisp_isValidContentElement(element)) {
            quotes.push({
                text: aisp_extractCleanText(element),
                cite: element.getAttribute('cite') || '',
                id: element.id || `quote-${index}`
            });
        }
    });

    return quotes;
}

/**
 * @function aisp_extractLinks - 提取链接信息
 * @description 提取页面中的重要链接
 * @returns {Array} 链接数组
 */
function aisp_extractLinks() {
    const links = [];
    const linkElements = document.querySelectorAll('a[href]');

    linkElements.forEach((link, index) => {
        if (aisp_isValidContentElement(link) && link.href && !link.href.startsWith('javascript:')) {
            const text = aisp_extractCleanText(link);
            if (text.length > 0 && text.length < 200) {
                links.push({
                    text: text,
                    href: link.href,
                    title: link.title || '',
                    target: link.target || '',
                    id: link.id || `link-${index}`
                });
            }
        }
    });

    return links.slice(0, 50); // 限制链接数量
}

/**
 * @function aisp_extractForms - 提取表单信息
 * @description 提取页面中的表单结构
 * @returns {Array} 表单数组
 */
function aisp_extractForms() {
    const forms = [];
    const formElements = document.querySelectorAll('form');

    formElements.forEach((form, index) => {
        if (aisp_isValidContentElement(form)) {
            const fields = [];
            const fieldElements = form.querySelectorAll('input, textarea, select');

            fieldElements.forEach(field => {
                if (field.type !== 'hidden') {
                    fields.push({
                        type: field.type || field.tagName.toLowerCase(),
                        name: field.name || '',
                        placeholder: field.placeholder || '',
                        required: field.required || false,
                        label: aisp_findFieldLabel(field)
                    });
                }
            });

            forms.push({
                action: form.action || '',
                method: form.method || 'get',
                fields: fields,
                id: form.id || `form-${index}`
            });
        }
    });

    return forms;
}

// #endregion

// #region 辅助工具函数

/**
 * @function aisp_isValidContentElement - 检查元素是否为有效内容
 * @description 判断元素是否应该被包含在内容分析中
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否为有效内容
 */
function aisp_isValidContentElement(element) {
    if (!element || !element.textContent) return false;

    // 检查元素是否可见
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
        return false;
    }

    // 检查元素是否在排除列表中
    return !aisp_isExcludedElement(element);
}

/**
 * @function aisp_isExcludedElement - 检查元素是否应该被排除
 * @description 判断元素是否在排除列表中
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否应该被排除
 */
function aisp_isExcludedElement(element) {
    for (const selector of CONTENT_SELECTORS.exclude) {
        if (element.matches && element.matches(selector)) {
            return true;
        }
        if (element.closest && element.closest(selector)) {
            return true;
        }
    }
    return false;
}

/**
 * @function aisp_extractCleanText - 提取干净的文本
 * @description 从元素中提取并清理文本内容
 * @param {Element} element - 源元素
 * @returns {string} 清理后的文本
 */
function aisp_extractCleanText(element) {
    if (!element) return '';

    // 克隆元素以避免修改原始DOM
    const clone = element.cloneNode(true);

    // 移除脚本和样式标签
    const scriptsAndStyles = clone.querySelectorAll('script, style, noscript');
    scriptsAndStyles.forEach(el => el.remove());

    // 获取文本内容并清理
    let text = clone.textContent || clone.innerText || '';

    // 清理多余的空白字符
    text = text.replace(/\s+/g, ' ').trim();

    return text;
}

/**
 * @function aisp_countWords - 计算单词数量
 * @description 计算元素中的单词数量
 * @param {Element} element - 要计算的元素
 * @returns {number} 单词数量
 */
function aisp_countWords(element) {
    const text = aisp_extractCleanText(element);
    if (!text) return 0;

    // 简单的单词计数（支持中英文）
    const words = text.match(/[\w\u4e00-\u9fff]+/g);
    return words ? words.length : 0;
}

/**
 * @function aisp_estimateReadingTime - 估算阅读时间
 * @description 基于文本长度估算阅读时间
 * @param {Element} element - 要分析的元素（可选）
 * @returns {number} 估算的阅读时间（分钟）
 */
function aisp_estimateReadingTime(element = document.body) {
    const wordCount = aisp_countWords(element);
    const wordsPerMinute = 200; // 平均阅读速度
    return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * @function aisp_detectLanguage - 检测页面语言
 * @description 检测页面的主要语言
 * @returns {string} 语言代码
 */
function aisp_detectLanguage() {
    // 首先检查HTML lang属性
    const htmlLang = document.documentElement.lang;
    if (htmlLang) return htmlLang;

    // 检查meta标签
    const metaLang = document.querySelector('meta[http-equiv="content-language"]');
    if (metaLang) return metaLang.content;

    // 简单的语言检测（基于字符）
    const text = aisp_extractCleanText(document.body).substring(0, 1000);
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    const japaneseChars = text.match(/[\u3040-\u309f\u30a0-\u30ff]/g);
    const koreanChars = text.match(/[\uac00-\ud7af]/g);

    if (chineseChars && chineseChars.length > 10) return 'zh';
    if (japaneseChars && japaneseChars.length > 5) return 'ja';
    if (koreanChars && koreanChars.length > 5) return 'ko';

    return 'en'; // 默认英语
}

/**
 * @function aisp_detectCodeLanguage - 检测代码语言
 * @description 检测代码块的编程语言
 * @param {Element} element - 代码元素
 * @returns {string} 编程语言
 */
function aisp_detectCodeLanguage(element) {
    // 检查class属性中的语言标识
    const className = element.className.toLowerCase();
    const languagePatterns = {
        'javascript': /\b(js|javascript)\b/,
        'python': /\bpython\b/,
        'java': /\bjava\b/,
        'css': /\bcss\b/,
        'html': /\bhtml\b/,
        'php': /\bphp\b/,
        'cpp': /\b(cpp|c\+\+)\b/,
        'c': /\bc\b/,
        'ruby': /\bruby\b/,
        'go': /\bgo\b/,
        'rust': /\brust\b/,
        'typescript': /\b(ts|typescript)\b/
    };

    for (const [lang, pattern] of Object.entries(languagePatterns)) {
        if (pattern.test(className)) {
            return lang;
        }
    }

    return 'unknown';
}

/**
 * @function aisp_findFieldLabel - 查找表单字段的标签
 * @description 查找与表单字段关联的标签文本
 * @param {Element} field - 表单字段元素
 * @returns {string} 标签文本
 */
function aisp_findFieldLabel(field) {
    // 检查for属性关联的label
    if (field.id) {
        const label = document.querySelector(`label[for="${field.id}"]`);
        if (label) return aisp_extractCleanText(label);
    }

    // 检查父级label
    const parentLabel = field.closest('label');
    if (parentLabel) return aisp_extractCleanText(parentLabel);

    // 检查前面的兄弟元素
    let sibling = field.previousElementSibling;
    while (sibling) {
        if (sibling.tagName === 'LABEL') {
            return aisp_extractCleanText(sibling);
        }
        sibling = sibling.previousElementSibling;
    }

    return '';
}

// #endregion

// #region 性能优化和缓存管理

/**
 * @function aisp_shouldRecapture - 判断是否需要重新捕获
 * @description 基于时间和内容变化判断是否需要重新捕获
 * @returns {boolean} 是否需要重新捕获
 */
function aisp_shouldRecapture() {
    const now = Date.now();
    const timeSinceLastCapture = now - aisp_lastCaptureTime;

    // 如果距离上次捕获时间太短，跳过
    if (timeSinceLastCapture < AISP_CONFIG.CONTENT_CAPTURE_DELAY) {
        return false;
    }

    aisp_lastCaptureTime = now;
    return true;
}

/**
 * @function aisp_cacheContentData - 缓存内容数据
 * @description 将内容数据存储到缓存中
 * @param {Object} contentData - 内容数据
 */
function aisp_cacheContentData(contentData) {
    const cacheKey = contentData.url + '_' + contentData.timestamp;
    aisp_contentCache.set(cacheKey, contentData);

    // 限制缓存大小
    if (aisp_contentCache.size > 10) {
        const firstKey = aisp_contentCache.keys().next().value;
        aisp_contentCache.delete(firstKey);
    }
}

/**
 * @function aisp_generateCaptureId - 生成捕获ID
 * @description 生成唯一的捕获标识符
 * @returns {string} 捕获ID
 */
function aisp_generateCaptureId() {
    return `capture_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * @function aisp_updatePerformanceMetrics - 更新性能指标
 * @description 更新内容捕获的性能统计
 * @param {number} startTime - 开始时间
 */
function aisp_updatePerformanceMetrics(startTime) {
    const duration = performance.now() - startTime;
    aisp_performanceMetrics.captureCount++;
    aisp_performanceMetrics.lastCaptureTime = duration;

    // 计算平均时间
    const totalTime = aisp_performanceMetrics.averageTime * (aisp_performanceMetrics.captureCount - 1) + duration;
    aisp_performanceMetrics.averageTime = totalTime / aisp_performanceMetrics.captureCount;
}

// #endregion

// #region 事件处理和监听器

/**
 * @function aisp_setupIntersectionObserver - 设置可见区域观察器
 * @description 监听元素进入和离开可视区域
 */
function aisp_setupIntersectionObserver() {
    if (!window.IntersectionObserver) return;

    aisp_intersectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 元素进入可视区域
                aisp_handleElementVisible(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    // 观察主要内容区域
    const mainElements = document.querySelectorAll(CONTENT_SELECTORS.main.join(', '));
    mainElements.forEach(element => {
        aisp_intersectionObserver.observe(element);
    });
}

/**
 * @function aisp_handleContentChange - 处理内容变化
 * @description 智能处理页面内容变化
 * @param {Array} mutations - 变化记录
 */
function aisp_handleContentChange(mutations) {
    let significantChange = false;

    mutations.forEach(mutation => {
        // 检查是否为重要变化
        if (aisp_isSignificantChange(mutation)) {
            significantChange = true;
        }
    });

    if (significantChange) {
        aisp_capturePageContent();
    }
}

/**
 * @function aisp_isSignificantChange - 判断是否为重要变化
 * @description 判断DOM变化是否足够重要需要重新捕获
 * @param {MutationRecord} mutation - 变化记录
 * @returns {boolean} 是否为重要变化
 */
function aisp_isSignificantChange(mutation) {
    // 忽略样式和类名变化
    if (mutation.type === 'attributes' &&
        ['style', 'class'].includes(mutation.attributeName)) {
        return false;
    }

    // 检查添加或删除的节点
    if (mutation.type === 'childList') {
        const addedNodes = Array.from(mutation.addedNodes);
        const removedNodes = Array.from(mutation.removedNodes);

        // 检查是否有重要的内容节点变化
        const importantNodes = [...addedNodes, ...removedNodes].filter(node => {
            return node.nodeType === Node.ELEMENT_NODE &&
                   aisp_isImportantElement(node);
        });

        return importantNodes.length > 0;
    }

    // 文本内容变化
    if (mutation.type === 'characterData') {
        const parent = mutation.target.parentElement;
        return parent && aisp_isImportantElement(parent);
    }

    return false;
}

/**
 * @function aisp_isImportantElement - 判断元素是否重要
 * @description 判断元素是否对内容分析重要
 * @param {Element} element - 要检查的元素
 * @returns {boolean} 是否重要
 */
function aisp_isImportantElement(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;

    const tagName = element.tagName.toLowerCase();
    const importantTags = ['article', 'main', 'section', 'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];

    return importantTags.includes(tagName) &&
           !aisp_isExcludedElement(element) &&
           aisp_extractCleanText(element).length > 10;
}

/**
 * @function aisp_handleElementVisible - 处理元素可见
 * @description 当元素进入可视区域时的处理
 * @param {Element} element - 可见的元素
 */
function aisp_handleElementVisible(element) {
    // 可以在这里添加懒加载内容的处理
    console.log('元素进入可视区域:', element);
}

/**
 * @function aisp_handleViewportChange - 处理视口变化
 * @description 处理页面滚动和窗口大小变化
 */
function aisp_handleViewportChange() {
    // 可以在这里添加视口变化的处理逻辑
    // 例如：检测新的可见内容、更新内容优先级等
}

// #endregion

// #region 错误处理和恢复

/**
 * @function aisp_setupErrorHandling - 设置错误处理
 * @description 设置全局错误处理机制
 */
function aisp_setupErrorHandling() {
    window.addEventListener('error', aisp_handleGlobalError);
    window.addEventListener('unhandledrejection', aisp_handleUnhandledRejection);
}

/**
 * @function aisp_handleInitializationError - 处理初始化错误
 * @description 处理初始化过程中的错误
 * @param {Error} error - 错误对象
 */
function aisp_handleInitializationError(error) {
    console.error('AI Side Panel 初始化错误:', error);

    // 尝试重新初始化
    setTimeout(() => {
        if (!aisp_isInitialized) {
            console.log('尝试重新初始化...');
            aisp_initializeContentScript();
        }
    }, 5000);
}

/**
 * @function aisp_handleCaptureError - 处理捕获错误
 * @description 处理内容捕获过程中的错误
 * @param {Error} error - 错误对象
 */
function aisp_handleCaptureError(error) {
    console.error('内容捕获错误:', error);

    // 记录错误信息
    try {
        chrome.runtime.sendMessage({
            action: 'capture_error',
            error: {
                message: error.message,
                stack: error.stack,
                url: window.location.href,
                timestamp: Date.now()
            }
        });
    } catch (e) {
        // 忽略发送错误
    }
}

/**
 * @function aisp_handleGlobalError - 处理全局错误
 * @description 处理全局JavaScript错误
 * @param {ErrorEvent} event - 错误事件
 */
function aisp_handleGlobalError(event) {
    if (event.filename && event.filename.includes('content-script.js')) {
        console.error('AI Side Panel 全局错误:', event.error);
        aisp_performanceMetrics.errorCount++;
    }
}

/**
 * @function aisp_handleUnhandledRejection - 处理未捕获的Promise拒绝
 * @description 处理未捕获的Promise错误
 * @param {PromiseRejectionEvent} event - Promise拒绝事件
 */
function aisp_handleUnhandledRejection(event) {
    console.error('AI Side Panel Promise拒绝:', event.reason);
    aisp_performanceMetrics.errorCount++;
}

// #endregion

// #region 性能监控

/**
 * @function aisp_setupPerformanceMonitoring - 设置性能监控
 * @description 设置性能监控和报告
 */
function aisp_setupPerformanceMonitoring() {
    // 定期报告性能指标
    setInterval(() => {
        if (aisp_performanceMetrics.captureCount > 0) {
            console.log('AI Side Panel 性能指标:', aisp_performanceMetrics);
        }
    }, 60000); // 每分钟报告一次
}

// #endregion

// #region 页面类型检测辅助函数

/**
 * @function aisp_identifyContentAreas - 识别内容区域
 * @description 识别页面中的不同内容区域
 * @returns {Object} 内容区域信息
 */
function aisp_identifyContentAreas() {
    return {
        header: document.querySelector('header, .header, #header'),
        navigation: document.querySelector('nav, .nav, .navigation'),
        main: document.querySelector('main, .main, .content'),
        sidebar: document.querySelector('aside, .sidebar, .side'),
        footer: document.querySelector('footer, .footer, #footer')
    };
}

/**
 * @function aisp_guessPageType - 猜测页面类型
 * @description 基于URL和内容特征猜测页面类型
 * @returns {string} 页面类型
 */
function aisp_guessPageType() {
    const url = window.location.href.toLowerCase();
    const pathname = window.location.pathname.toLowerCase();

    // 基于URL模式判断
    if (pathname.includes('/article/') || pathname.includes('/post/') || pathname.includes('/blog/')) {
        return 'article';
    }
    if (pathname.includes('/product/') || pathname.includes('/item/') || url.includes('shop')) {
        return 'product';
    }
    if (pathname.includes('/search') || url.includes('search=')) {
        return 'search';
    }
    if (pathname.includes('/contact') || pathname.includes('/form')) {
        return 'form';
    }
    if (pathname === '/' || pathname === '/index.html') {
        return 'homepage';
    }

    return 'unknown';
}

/**
 * @function aisp_isValidImageElement - 检查图片元素是否有效
 * @description 判断图片是否应该被包含在分析中
 * @param {Element} img - 图片元素
 * @returns {boolean} 是否为有效图片
 */
function aisp_isValidImageElement(img) {
    if (!img || !img.src) return false;

    // 检查图片尺寸
    const width = img.offsetWidth || img.naturalWidth || 0;
    const height = img.offsetHeight || img.naturalHeight || 0;

    if (width < AISP_CONFIG.MIN_IMAGE_SIZE || height < AISP_CONFIG.MIN_IMAGE_SIZE) {
        return false;
    }

    // 检查图片是否可见
    const style = window.getComputedStyle(img);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
        return false;
    }

    // 排除装饰性图片
    const src = img.src.toLowerCase();
    const decorativePatterns = [
        'icon', 'logo', 'avatar', 'thumbnail', 'badge', 'button',
        'arrow', 'bullet', 'separator', 'spacer', 'pixel'
    ];

    for (const pattern of decorativePatterns) {
        if (src.includes(pattern) && (width < 100 || height < 100)) {
            return false;
        }
    }

    return true;
}

/**
 * @function aisp_calculateImageImportance - 计算图片重要性
 * @description 基于多个因素计算图片的重要性分数
 * @param {Element} img - 图片元素
 * @returns {number} 重要性分数 (0-1)
 */
function aisp_calculateImageImportance(img) {
    let score = 0;

    const width = img.offsetWidth || img.naturalWidth || 0;
    const height = img.offsetHeight || img.naturalHeight || 0;
    const area = width * height;

    // 尺寸分数 (0-0.4)
    if (area > 100000) score += 0.4;      // 大图片
    else if (area > 50000) score += 0.3;  // 中等图片
    else if (area > 10000) score += 0.2;  // 小图片
    else score += 0.1;                    // 很小的图片

    // 位置分数 (0-0.3)
    const rect = img.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    if (rect.top < viewportHeight) {
        score += 0.3; // 在首屏可见
    } else if (rect.top < viewportHeight * 2) {
        score += 0.2; // 在第二屏
    } else {
        score += 0.1; // 更下方
    }

    // Alt文本分数 (0-0.2)
    if (img.alt && img.alt.length > 5) {
        score += 0.2;
    } else if (img.alt) {
        score += 0.1;
    }

    // 父元素重要性 (0-0.1)
    const parent = img.closest('article, main, .content, .post');
    if (parent) {
        score += 0.1;
    }

    return Math.max(0, Math.min(1, score));
}

console.log('AI Side Panel Content Script 已加载');
// #endregion

// #region 消息监听
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'show_template_popup') {
        aisp_showTemplatePopup(request.data);
        sendResponse({ success: true });
    } else if (request.action === 'get_page_content') {
        // 处理获取页面内容的请求
        aisp_handleGetPageContentRequest(sendResponse);
        return true; // 异步响应
    }

    return true; // 保持消息通道开放
});

/**
 * @function aisp_handleGetPageContentRequest - 处理获取页面内容请求
 * @description 响应侧边栏请求页面内容的消息
 * @param {Function} sendResponse - 响应函数
 */
async function aisp_handleGetPageContentRequest(sendResponse) {
    try {
        // 重新捕获最新的页面内容
        await aisp_capturePageContent();

        // 准备返回的内容数据
        const contentData = {
            text: aisp_extractText(),
            url: window.location.href,
            title: document.title,
            structuredContent: aisp_extractStructuredContent(),
            metadata: aisp_extractMetadata(),
            timestamp: Date.now()
        };

        sendResponse({
            success: true,
            data: contentData
        });

    } catch (error) {
        console.error('获取页面内容失败:', error);
        sendResponse({
            success: false,
            error: error.message || '获取页面内容失败'
        });
    }
}
// #endregion

// #region 高级内容提取技术模块

/**
 * @class AdvancedContentExtractor - 高级内容提取器
 * @description 提供高级内容提取功能，应对复杂网站场景和反爬虫机制
 */
class AdvancedContentExtractor {
    constructor() {
        this.extractionCache = new Map();
        this.retryAttempts = new Map();
        this.lastExtractionTime = 0;
        this.qualityScores = new Map();
        this.robotsRules = null;
        this.isInitialized = false;

        contentLogger.info('高级内容提取器初始化');
    }

    /**
     * @function initialize - 初始化高级提取器
     * @description 设置高级提取功能和反爬虫机制
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            // 检查robots.txt规则
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ANTI_DETECTION.RESPECT_ROBOTS_TXT) {
                await this.loadRobotsRules();
            }

            // 设置动态内容监听
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_DYNAMIC_CONTENT) {
                this.setupDynamicContentDetection();
            }

            // 设置SPA路由监听
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_SPA_ROUTING) {
                this.setupSPARouteDetection();
            }

            this.isInitialized = true;
            contentLogger.info('高级内容提取器初始化完成');

        } catch (error) {
            contentLogger.error('高级内容提取器初始化失败', { error: error.message });
        }
    }

    /**
     * @function extractAdvancedContent - 执行高级内容提取
     * @description 使用多种技术提取页面内容
     * @returns {Promise<Object>} 提取的内容数据
     */
    async extractAdvancedContent() {
        const startTime = performance.now();

        try {
            // 检查频率限制
            if (!this.checkRateLimit()) {
                contentLogger.warn('触发频率限制，跳过本次提取');
                return null;
            }

            const extractedContent = {
                standard: await this.extractStandardContent(),
                shadowDOM: null,
                iframes: null,
                canvas: null,
                svg: null,
                dynamic: null,
                quality: 0
            };

            // Shadow DOM内容提取
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_SHADOW_DOM) {
                extractedContent.shadowDOM = await this.extractShadowDOMContent();
            }

            // iframe内容提取
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_IFRAME_EXTRACTION) {
                extractedContent.iframes = await this.extractIframeContent();
            }

            // Canvas文字识别
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_CANVAS_OCR) {
                extractedContent.canvas = await this.extractCanvasText();
            }

            // SVG文字提取
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_SVG_TEXT) {
                extractedContent.svg = await this.extractSVGText();
            }

            // 动态内容检测
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ENABLE_DYNAMIC_CONTENT) {
                extractedContent.dynamic = await this.extractDynamicContent();
            }

            // 计算内容质量分数
            extractedContent.quality = this.calculateContentQuality(extractedContent);

            // 缓存结果
            this.cacheExtractionResult(extractedContent);

            const duration = performance.now() - startTime;
            contentLogger.performance('高级内容提取', duration, {
                quality: extractedContent.quality,
                hasCanvasText: !!extractedContent.canvas,
                hasShadowDOM: !!extractedContent.shadowDOM,
                hasIframes: !!extractedContent.iframes
            });

            return extractedContent;

        } catch (error) {
            contentLogger.error('高级内容提取失败', {
                error: error.message,
                stack: error.stack
            });
            return null;
        }
    }

    /**
     * @function extractShadowDOMContent - 提取Shadow DOM内容
     * @description 遍历Shadow DOM树提取内容
     * @returns {Promise<Object>} Shadow DOM内容
     */
    async extractShadowDOMContent() {
        try {
            const shadowContent = {
                text: '',
                elements: [],
                count: 0
            };

            // 查找所有可能包含Shadow DOM的元素
            const elementsWithShadow = document.querySelectorAll('*');

            for (const element of elementsWithShadow) {
                if (element.shadowRoot) {
                    const shadowText = this.extractTextFromShadowRoot(element.shadowRoot);
                    if (shadowText.trim()) {
                        shadowContent.text += shadowText + '\n';
                        shadowContent.elements.push({
                            tagName: element.tagName,
                            text: shadowText,
                            selector: this.generateElementSelector(element)
                        });
                        shadowContent.count++;
                    }
                }
            }

            contentLogger.debug('Shadow DOM内容提取完成', {
                elementsFound: shadowContent.count,
                textLength: shadowContent.text.length
            });

            return shadowContent.count > 0 ? shadowContent : null;

        } catch (error) {
            contentLogger.error('Shadow DOM内容提取失败', { error: error.message });
            return null;
        }
    }

    /**
     * @function extractTextFromShadowRoot - 从Shadow Root提取文本
     * @param {ShadowRoot} shadowRoot - Shadow Root对象
     * @returns {string} 提取的文本
     */
    extractTextFromShadowRoot(shadowRoot) {
        try {
            let text = '';

            // 递归遍历Shadow DOM树
            const walker = document.createTreeWalker(
                shadowRoot,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: (node) => {
                        // 过滤掉脚本和样式中的文本
                        const parent = node.parentElement;
                        if (parent && ['SCRIPT', 'STYLE', 'NOSCRIPT'].includes(parent.tagName)) {
                            return NodeFilter.FILTER_REJECT;
                        }
                        return NodeFilter.FILTER_ACCEPT;
                    }
                }
            );

            let node;
            while (node = walker.nextNode()) {
                const nodeText = node.textContent.trim();
                if (nodeText) {
                    text += nodeText + ' ';
                }
            }

            return text.trim();

        } catch (error) {
            contentLogger.error('Shadow Root文本提取失败', { error: error.message });
            return '';
        }
    }

    /**
     * @function extractIframeContent - 提取iframe内容
     * @description 安全地提取iframe中的内容
     * @returns {Promise<Object>} iframe内容
     */
    async extractIframeContent() {
        try {
            const iframeContent = {
                frames: [],
                totalText: '',
                count: 0
            };

            const iframes = document.querySelectorAll('iframe');

            for (const iframe of iframes) {
                try {
                    // 检查是否可以访问iframe内容（同源策略）
                    if (this.canAccessIframe(iframe)) {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const text = this.extractTextFromDocument(iframeDoc);

                        if (text.trim()) {
                            iframeContent.frames.push({
                                src: iframe.src,
                                text: text,
                                title: iframe.title || '',
                                selector: this.generateElementSelector(iframe)
                            });
                            iframeContent.totalText += text + '\n';
                            iframeContent.count++;
                        }
                    } else {
                        // 对于跨域iframe，尝试获取基本信息
                        iframeContent.frames.push({
                            src: iframe.src,
                            text: '',
                            title: iframe.title || '',
                            selector: this.generateElementSelector(iframe),
                            crossOrigin: true
                        });
                    }
                } catch (error) {
                    // 跨域访问被阻止，记录但继续处理
                    contentLogger.debug('iframe访问被阻止', {
                        src: iframe.src,
                        error: error.message
                    });
                }
            }

            contentLogger.debug('iframe内容提取完成', {
                totalFrames: iframes.length,
                accessibleFrames: iframeContent.count,
                textLength: iframeContent.totalText.length
            });

            return iframeContent.count > 0 ? iframeContent : null;

        } catch (error) {
            contentLogger.error('iframe内容提取失败', { error: error.message });
            return null;
        }
    }

    /**
     * @function canAccessIframe - 检查是否可以访问iframe
     * @param {HTMLIFrameElement} iframe - iframe元素
     * @returns {boolean} 是否可以访问
     */
    canAccessIframe(iframe) {
        try {
            // 尝试访问iframe的document
            const doc = iframe.contentDocument || iframe.contentWindow.document;
            return !!doc;
        } catch (error) {
            return false;
        }
    }

    /**
     * @function extractTextFromDocument - 从文档提取文本
     * @param {Document} doc - 文档对象
     * @returns {string} 提取的文本
     */
    extractTextFromDocument(doc) {
        try {
            // 排除脚本和样式
            const excludeSelectors = 'script, style, noscript';
            const excludeElements = doc.querySelectorAll(excludeSelectors);

            excludeElements.forEach(el => {
                el.style.display = 'none';
            });

            const text = doc.body ? (doc.body.innerText || doc.body.textContent || '') : '';

            // 恢复元素
            excludeElements.forEach(el => {
                el.style.display = '';
            });

            return text.replace(/\s+/g, ' ').trim();

        } catch (error) {
            contentLogger.error('文档文本提取失败', { error: error.message });
            return '';
        }
    }

    /**
     * @function extractCanvasText - 提取Canvas中的文本
     * @description 使用OCR技术识别Canvas中的文字
     * @returns {Promise<Object>} Canvas文本内容
     */
    async extractCanvasText() {
        try {
            const canvasContent = {
                elements: [],
                totalText: '',
                count: 0
            };

            const canvases = document.querySelectorAll('canvas');

            for (const canvas of canvases) {
                try {
                    // 检查canvas是否包含内容
                    if (this.hasCanvasContent(canvas)) {
                        const text = await this.performCanvasOCR(canvas);

                        if (text.trim()) {
                            canvasContent.elements.push({
                                text: text,
                                width: canvas.width,
                                height: canvas.height,
                                selector: this.generateElementSelector(canvas)
                            });
                            canvasContent.totalText += text + '\n';
                            canvasContent.count++;
                        }
                    }
                } catch (error) {
                    contentLogger.debug('Canvas文本提取失败', {
                        error: error.message,
                        canvas: this.generateElementSelector(canvas)
                    });
                }
            }

            contentLogger.debug('Canvas文本提取完成', {
                totalCanvases: canvases.length,
                textCanvases: canvasContent.count,
                textLength: canvasContent.totalText.length
            });

            return canvasContent.count > 0 ? canvasContent : null;

        } catch (error) {
            contentLogger.error('Canvas文本提取失败', { error: error.message });
            return null;
        }
    }

    /**
     * @function hasCanvasContent - 检查Canvas是否包含内容
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @returns {boolean} 是否包含内容
     */
    hasCanvasContent(canvas) {
        try {
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // 检查是否有非透明像素
            for (let i = 3; i < data.length; i += 4) {
                if (data[i] > 0) { // alpha通道
                    return true;
                }
            }
            return false;
        } catch (error) {
            // 如果无法访问canvas内容（可能是跨域），假设有内容
            return true;
        }
    }

    /**
     * @function performCanvasOCR - 对Canvas执行OCR识别
     * @param {HTMLCanvasElement} canvas - Canvas元素
     * @returns {Promise<string>} 识别的文本
     */
    async performCanvasOCR(canvas) {
        try {
            // 简化的文本检测方法（实际OCR需要专门的库）
            // 这里我们尝试检测常见的文本模式

            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            // 检查是否有文本特征（简单的启发式方法）
            const hasTextFeatures = this.detectTextFeatures(imageData);

            if (hasTextFeatures) {
                // 在实际应用中，这里应该调用OCR API或库
                // 目前返回占位符文本
                return `[Canvas文本内容 - 尺寸: ${canvas.width}x${canvas.height}]`;
            }

            return '';

        } catch (error) {
            contentLogger.debug('Canvas OCR失败', { error: error.message });
            return '';
        }
    }

    /**
     * @function detectTextFeatures - 检测图像中的文本特征
     * @param {ImageData} imageData - 图像数据
     * @returns {boolean} 是否包含文本特征
     */
    detectTextFeatures(imageData) {
        try {
            const data = imageData.data;
            const width = imageData.width;
            const height = imageData.height;

            // 简单的边缘检测来识别可能的文本
            let edgeCount = 0;
            const threshold = 50;

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    const idx = (y * width + x) * 4;
                    const gray = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;

                    const rightIdx = (y * width + x + 1) * 4;
                    const rightGray = (data[rightIdx] + data[rightIdx + 1] + data[rightIdx + 2]) / 3;

                    const bottomIdx = ((y + 1) * width + x) * 4;
                    const bottomGray = (data[bottomIdx] + data[bottomIdx + 1] + data[bottomIdx + 2]) / 3;

                    if (Math.abs(gray - rightGray) > threshold || Math.abs(gray - bottomGray) > threshold) {
                        edgeCount++;
                    }
                }
            }

            // 如果边缘密度超过阈值，可能包含文本
            const edgeDensity = edgeCount / (width * height);
            return edgeDensity > 0.01; // 1%的像素是边缘

        } catch (error) {
            return false;
        }
    }

    /**
     * @function extractSVGText - 提取SVG中的文本
     * @description 提取SVG元素中的文本内容
     * @returns {Promise<Object>} SVG文本内容
     */
    async extractSVGText() {
        try {
            const svgContent = {
                elements: [],
                totalText: '',
                count: 0
            };

            const svgs = document.querySelectorAll('svg');

            for (const svg of svgs) {
                const textElements = svg.querySelectorAll('text, tspan, textPath');
                let svgText = '';

                textElements.forEach(textEl => {
                    const text = textEl.textContent.trim();
                    if (text) {
                        svgText += text + ' ';
                    }
                });

                if (svgText.trim()) {
                    svgContent.elements.push({
                        text: svgText.trim(),
                        selector: this.generateElementSelector(svg),
                        textElementsCount: textElements.length
                    });
                    svgContent.totalText += svgText + '\n';
                    svgContent.count++;
                }
            }

            contentLogger.debug('SVG文本提取完成', {
                totalSVGs: svgs.length,
                textSVGs: svgContent.count,
                textLength: svgContent.totalText.length
            });

            return svgContent.count > 0 ? svgContent : null;

        } catch (error) {
            contentLogger.error('SVG文本提取失败', { error: error.message });
            return null;
        }
    }

    /**
     * @function extractDynamicContent - 提取动态加载的内容
     * @description 检测并提取AJAX、懒加载等动态内容
     * @returns {Promise<Object>} 动态内容
     */
    async extractDynamicContent() {
        try {
            const dynamicContent = {
                lazyLoaded: [],
                ajaxContent: [],
                infiniteScroll: null,
                totalNewContent: '',
                count: 0
            };

            // 检测懒加载图片和内容
            const lazyElements = await this.detectLazyLoadedContent();
            if (lazyElements.length > 0) {
                dynamicContent.lazyLoaded = lazyElements;
                dynamicContent.count += lazyElements.length;
            }

            // 检测AJAX加载的内容
            const ajaxContent = await this.detectAjaxContent();
            if (ajaxContent.length > 0) {
                dynamicContent.ajaxContent = ajaxContent;
                dynamicContent.count += ajaxContent.length;
            }

            // 检测无限滚动
            const infiniteScrollData = await this.detectInfiniteScroll();
            if (infiniteScrollData) {
                dynamicContent.infiniteScroll = infiniteScrollData;
                dynamicContent.count++;
            }

            contentLogger.debug('动态内容检测完成', {
                lazyElements: dynamicContent.lazyLoaded.length,
                ajaxElements: dynamicContent.ajaxContent.length,
                hasInfiniteScroll: !!dynamicContent.infiniteScroll
            });

            return dynamicContent.count > 0 ? dynamicContent : null;

        } catch (error) {
            contentLogger.error('动态内容提取失败', { error: error.message });
            return null;
        }
    }

    /**
     * @function detectLazyLoadedContent - 检测懒加载内容
     * @returns {Promise<Array>} 懒加载元素列表
     */
    async detectLazyLoadedContent() {
        try {
            const lazyElements = [];

            // 常见的懒加载属性
            const lazySelectors = [
                '[data-src]',
                '[data-lazy]',
                '[data-original]',
                '[loading="lazy"]',
                '.lazy',
                '.lazyload'
            ];

            for (const selector of lazySelectors) {
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {
                    if (this.isElementInViewport(el) || this.shouldTriggerLazyLoad(el)) {
                        lazyElements.push({
                            tagName: el.tagName,
                            selector: this.generateElementSelector(el),
                            attributes: this.getRelevantAttributes(el),
                            isVisible: this.isElementVisible(el)
                        });
                    }
                });
            }

            return lazyElements;

        } catch (error) {
            contentLogger.error('懒加载内容检测失败', { error: error.message });
            return [];
        }
    }

    /**
     * @function detectAjaxContent - 检测AJAX加载的内容
     * @returns {Promise<Array>} AJAX内容列表
     */
    async detectAjaxContent() {
        try {
            const ajaxContent = [];

            // 监听网络请求（通过拦截fetch和XMLHttpRequest）
            const originalFetch = window.fetch;
            const originalXHR = window.XMLHttpRequest;

            // 检查是否有动态加载的内容容器
            const dynamicContainers = document.querySelectorAll(
                '[data-ajax], [data-load], .ajax-content, .dynamic-content, .load-more'
            );

            dynamicContainers.forEach(container => {
                if (this.hasRecentlyChangedContent(container)) {
                    ajaxContent.push({
                        selector: this.generateElementSelector(container),
                        content: this.extractTextFromElement(container),
                        lastModified: this.getElementLastModified(container)
                    });
                }
            });

            return ajaxContent;

        } catch (error) {
            contentLogger.error('AJAX内容检测失败', { error: error.message });
            return [];
        }
    }

    /**
     * @function detectInfiniteScroll - 检测无限滚动
     * @returns {Promise<Object|null>} 无限滚动信息
     */
    async detectInfiniteScroll() {
        try {
            // 检测无限滚动的常见模式
            const infiniteScrollIndicators = [
                '.infinite-scroll',
                '.endless-scroll',
                '[data-infinite]',
                '.load-more-trigger',
                '.pagination-loading'
            ];

            for (const selector of infiniteScrollIndicators) {
                const element = document.querySelector(selector);
                if (element) {
                    return {
                        type: 'infinite-scroll',
                        trigger: selector,
                        position: this.getElementPosition(element),
                        isActive: this.isElementVisible(element)
                    };
                }
            }

            // 检测滚动到底部加载更多的模式
            const scrollHeight = document.documentElement.scrollHeight;
            const clientHeight = document.documentElement.clientHeight;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollHeight - (scrollTop + clientHeight) < 100) {
                // 接近页面底部，可能有无限滚动
                return {
                    type: 'scroll-bottom',
                    scrollPosition: scrollTop,
                    remainingHeight: scrollHeight - (scrollTop + clientHeight)
                };
            }

            return null;

        } catch (error) {
            contentLogger.error('无限滚动检测失败', { error: error.message });
            return null;
        }
    }

    /**
     * @function checkRateLimit - 检查频率限制
     * @description 实现请求频率控制，避免被反爬虫系统检测
     * @returns {boolean} 是否允许执行
     */
    checkRateLimit() {
        if (!AISP_CONFIG.ADVANCED_EXTRACTION.ANTI_DETECTION.RATE_LIMITING) {
            return true;
        }

        const now = Date.now();
        const minInterval = AISP_CONFIG.ADVANCED_EXTRACTION.PERFORMANCE.RETRY_DELAY_BASE;

        if (now - this.lastExtractionTime < minInterval) {
            return false;
        }

        this.lastExtractionTime = now;
        return true;
    }

    /**
     * @function simulateHumanBehavior - 模拟人类行为
     * @description 添加随机延迟和行为模式，避免被检测为机器人
     */
    async simulateHumanBehavior() {
        if (!AISP_CONFIG.ADVANCED_EXTRACTION.ANTI_DETECTION.SIMULATE_HUMAN_BEHAVIOR) {
            return;
        }

        try {
            // 随机延迟
            if (AISP_CONFIG.ADVANCED_EXTRACTION.ANTI_DETECTION.RANDOMIZE_TIMING) {
                const delay = Math.random() * 2000 + 500; // 500-2500ms随机延迟
                await this.sleep(delay);
            }

            // 模拟鼠标移动（轻微的）
            this.simulateMouseMovement();

            // 模拟滚动行为
            this.simulateScrollBehavior();

        } catch (error) {
            contentLogger.debug('人类行为模拟失败', { error: error.message });
        }
    }

    /**
     * @function simulateMouseMovement - 模拟鼠标移动
     */
    simulateMouseMovement() {
        try {
            const event = new MouseEvent('mousemove', {
                clientX: Math.random() * window.innerWidth,
                clientY: Math.random() * window.innerHeight,
                bubbles: true
            });
            document.dispatchEvent(event);
        } catch (error) {
            // 忽略错误
        }
    }

    /**
     * @function simulateScrollBehavior - 模拟滚动行为
     */
    simulateScrollBehavior() {
        try {
            const currentScroll = window.pageYOffset;
            const smallScroll = Math.random() * 50 - 25; // -25到25像素的小幅滚动
            window.scrollTo(0, currentScroll + smallScroll);

            // 立即滚回原位置
            setTimeout(() => {
                window.scrollTo(0, currentScroll);
            }, 100);
        } catch (error) {
            // 忽略错误
        }
    }

    /**
     * @function loadRobotsRules - 加载robots.txt规则
     * @description 检查并遵守网站的robots.txt规则
     */
    async loadRobotsRules() {
        try {
            const robotsUrl = new URL('/robots.txt', window.location.origin).href;

            const response = await fetch(robotsUrl, {
                method: 'GET',
                cache: 'force-cache'
            });

            if (response.ok) {
                const robotsText = await response.text();
                this.robotsRules = this.parseRobotsRules(robotsText);
                contentLogger.debug('robots.txt规则加载完成');
            }

        } catch (error) {
            contentLogger.debug('robots.txt加载失败', { error: error.message });
            // 如果无法加载robots.txt，假设允许访问
            this.robotsRules = { allowed: true };
        }
    }

    /**
     * @function parseRobotsRules - 解析robots.txt规则
     * @param {string} robotsText - robots.txt内容
     * @returns {Object} 解析后的规则
     */
    parseRobotsRules(robotsText) {
        try {
            const rules = {
                userAgents: {},
                sitemaps: [],
                crawlDelay: 0
            };

            const lines = robotsText.split('\n');
            let currentUserAgent = '*';

            for (const line of lines) {
                const trimmedLine = line.trim().toLowerCase();

                if (trimmedLine.startsWith('user-agent:')) {
                    currentUserAgent = trimmedLine.split(':')[1].trim();
                    if (!rules.userAgents[currentUserAgent]) {
                        rules.userAgents[currentUserAgent] = {
                            disallow: [],
                            allow: []
                        };
                    }
                } else if (trimmedLine.startsWith('disallow:')) {
                    const path = trimmedLine.split(':')[1].trim();
                    if (rules.userAgents[currentUserAgent]) {
                        rules.userAgents[currentUserAgent].disallow.push(path);
                    }
                } else if (trimmedLine.startsWith('allow:')) {
                    const path = trimmedLine.split(':')[1].trim();
                    if (rules.userAgents[currentUserAgent]) {
                        rules.userAgents[currentUserAgent].allow.push(path);
                    }
                } else if (trimmedLine.startsWith('crawl-delay:')) {
                    const delay = parseInt(trimmedLine.split(':')[1].trim());
                    rules.crawlDelay = Math.max(rules.crawlDelay, delay * 1000);
                }
            }

            return rules;

        } catch (error) {
            contentLogger.error('robots.txt解析失败', { error: error.message });
            return { allowed: true };
        }
    }

    /**
     * @function setupSPARouteDetection - 设置SPA路由检测
     * @description 监听单页应用的路由变化
     */
    setupSPARouteDetection() {
        try {
            let lastUrl = window.location.href;

            // 监听popstate事件（浏览器前进后退）
            window.addEventListener('popstate', () => {
                this.handleRouteChange(lastUrl, window.location.href);
                lastUrl = window.location.href;
            });

            // 监听pushState和replaceState
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;

            history.pushState = function(...args) {
                originalPushState.apply(history, args);
                setTimeout(() => {
                    if (window.location.href !== lastUrl) {
                        advancedExtractor.handleRouteChange(lastUrl, window.location.href);
                        lastUrl = window.location.href;
                    }
                }, 100);
            };

            history.replaceState = function(...args) {
                originalReplaceState.apply(history, args);
                setTimeout(() => {
                    if (window.location.href !== lastUrl) {
                        advancedExtractor.handleRouteChange(lastUrl, window.location.href);
                        lastUrl = window.location.href;
                    }
                }, 100);
            };

            contentLogger.debug('SPA路由检测已设置');

        } catch (error) {
            contentLogger.error('SPA路由检测设置失败', { error: error.message });
        }
    }

    /**
     * @function handleRouteChange - 处理路由变化
     * @param {string} oldUrl - 旧URL
     * @param {string} newUrl - 新URL
     */
    async handleRouteChange(oldUrl, newUrl) {
        try {
            contentLogger.info('检测到SPA路由变化', { oldUrl, newUrl });

            // 等待新内容加载
            await this.sleep(1000);

            // 重新提取内容
            const newContent = await this.extractAdvancedContent();

            if (newContent && newContent.quality > AISP_CONFIG.ADVANCED_EXTRACTION.PERFORMANCE.QUALITY_THRESHOLD) {
                // 发送路由变化事件
                chrome.runtime.sendMessage({
                    action: 'spa_route_changed',
                    data: {
                        oldUrl,
                        newUrl,
                        content: newContent,
                        timestamp: Date.now()
                    }
                });
            }

        } catch (error) {
            contentLogger.error('路由变化处理失败', { error: error.message });
        }
    }

    /**
     * @function setupDynamicContentDetection - 设置动态内容检测
     * @description 监听DOM变化，检测动态加载的内容
     */
    setupDynamicContentDetection() {
        try {
            const observer = new MutationObserver((mutations) => {
                let hasSignificantChange = false;

                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                const textContent = node.textContent || '';
                                if (textContent.trim().length > 50) {
                                    hasSignificantChange = true;
                                }
                            }
                        });
                    }
                });

                if (hasSignificantChange) {
                    // 延迟处理，避免频繁触发
                    clearTimeout(this.dynamicContentTimeout);
                    this.dynamicContentTimeout = setTimeout(() => {
                        this.handleDynamicContentChange();
                    }, 2000);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: false
            });

            this.dynamicContentObserver = observer;
            contentLogger.debug('动态内容检测已设置');

        } catch (error) {
            contentLogger.error('动态内容检测设置失败', { error: error.message });
        }
    }

    /**
     * @function handleDynamicContentChange - 处理动态内容变化
     */
    async handleDynamicContentChange() {
        try {
            contentLogger.debug('检测到动态内容变化');

            const dynamicContent = await this.extractDynamicContent();

            if (dynamicContent && dynamicContent.count > 0) {
                // 发送动态内容变化事件
                chrome.runtime.sendMessage({
                    action: 'dynamic_content_detected',
                    data: {
                        content: dynamicContent,
                        url: window.location.href,
                        timestamp: Date.now()
                    }
                });
            }

        } catch (error) {
            contentLogger.error('动态内容变化处理失败', { error: error.message });
        }
    }

    // #region 工具函数

    /**
     * @function generateElementSelector - 生成元素选择器
     * @param {Element} element - DOM元素
     * @returns {string} CSS选择器
     */
    generateElementSelector(element) {
        try {
            if (element.id) {
                return `#${element.id}`;
            }

            if (element.className) {
                const classes = element.className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    return `.${classes.join('.')}`;
                }
            }

            const tagName = element.tagName.toLowerCase();
            const parent = element.parentElement;

            if (parent) {
                const siblings = Array.from(parent.children).filter(el => el.tagName === element.tagName);
                if (siblings.length > 1) {
                    const index = siblings.indexOf(element) + 1;
                    return `${tagName}:nth-of-type(${index})`;
                }
            }

            return tagName;

        } catch (error) {
            return 'unknown';
        }
    }

    /**
     * @function isElementInViewport - 检查元素是否在视口中
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否在视口中
     */
    isElementInViewport(element) {
        try {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        } catch (error) {
            return false;
        }
    }

    /**
     * @function isElementVisible - 检查元素是否可见
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否可见
     */
    isElementVisible(element) {
        try {
            const style = window.getComputedStyle(element);
            return style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0';
        } catch (error) {
            return false;
        }
    }

    /**
     * @function getRelevantAttributes - 获取元素的相关属性
     * @param {Element} element - DOM元素
     * @returns {Object} 属性对象
     */
    getRelevantAttributes(element) {
        try {
            const relevantAttrs = ['data-src', 'data-lazy', 'data-original', 'src', 'href', 'alt', 'title'];
            const attrs = {};

            relevantAttrs.forEach(attr => {
                if (element.hasAttribute(attr)) {
                    attrs[attr] = element.getAttribute(attr);
                }
            });

            return attrs;
        } catch (error) {
            return {};
        }
    }

    /**
     * @function shouldTriggerLazyLoad - 判断是否应该触发懒加载
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否应该触发
     */
    shouldTriggerLazyLoad(element) {
        try {
            // 检查元素是否接近视口
            const rect = element.getBoundingClientRect();
            const threshold = 200; // 200px阈值

            return rect.top < window.innerHeight + threshold &&
                   rect.bottom > -threshold;
        } catch (error) {
            return false;
        }
    }

    /**
     * @function hasRecentlyChangedContent - 检查元素内容是否最近发生变化
     * @param {Element} element - DOM元素
     * @returns {boolean} 是否最近变化
     */
    hasRecentlyChangedContent(element) {
        try {
            const elementId = this.generateElementSelector(element);
            const currentContent = element.textContent || '';
            const lastContent = this.extractionCache.get(elementId);

            if (lastContent && lastContent !== currentContent) {
                this.extractionCache.set(elementId, currentContent);
                return true;
            }

            if (!lastContent) {
                this.extractionCache.set(elementId, currentContent);
                return currentContent.trim().length > 0;
            }

            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * @function extractTextFromElement - 从元素提取文本
     * @param {Element} element - DOM元素
     * @returns {string} 提取的文本
     */
    extractTextFromElement(element) {
        try {
            return (element.textContent || element.innerText || '').trim();
        } catch (error) {
            return '';
        }
    }

    /**
     * @function getElementLastModified - 获取元素最后修改时间
     * @param {Element} element - DOM元素
     * @returns {number} 时间戳
     */
    getElementLastModified(element) {
        try {
            // 简单的启发式方法
            const dataAttrs = element.dataset;
            if (dataAttrs.lastModified) {
                return parseInt(dataAttrs.lastModified);
            }
            return Date.now();
        } catch (error) {
            return Date.now();
        }
    }

    /**
     * @function getElementPosition - 获取元素位置
     * @param {Element} element - DOM元素
     * @returns {Object} 位置信息
     */
    getElementPosition(element) {
        try {
            const rect = element.getBoundingClientRect();
            return {
                top: rect.top + window.pageYOffset,
                left: rect.left + window.pageXOffset,
                width: rect.width,
                height: rect.height
            };
        } catch (error) {
            return { top: 0, left: 0, width: 0, height: 0 };
        }
    }

    /**
     * @function calculateContentQuality - 计算内容质量分数
     * @param {Object} content - 提取的内容
     * @returns {number} 质量分数 (0-1)
     */
    calculateContentQuality(content) {
        try {
            let score = 0;
            let factors = 0;

            // 标准内容质量
            if (content.standard && content.standard.text) {
                const textLength = content.standard.text.length;
                score += Math.min(textLength / 1000, 1) * 0.4; // 最多0.4分
                factors++;
            }

            // Shadow DOM内容
            if (content.shadowDOM && content.shadowDOM.count > 0) {
                score += 0.2;
                factors++;
            }

            // iframe内容
            if (content.iframes && content.iframes.count > 0) {
                score += 0.15;
                factors++;
            }

            // Canvas文本
            if (content.canvas && content.canvas.count > 0) {
                score += 0.1;
                factors++;
            }

            // SVG文本
            if (content.svg && content.svg.count > 0) {
                score += 0.1;
                factors++;
            }

            // 动态内容
            if (content.dynamic && content.dynamic.count > 0) {
                score += 0.05;
                factors++;
            }

            return factors > 0 ? Math.min(score, 1) : 0;

        } catch (error) {
            return 0;
        }
    }

    /**
     * @function cacheExtractionResult - 缓存提取结果
     * @param {Object} content - 提取的内容
     */
    cacheExtractionResult(content) {
        try {
            const cacheKey = window.location.href;
            const cacheData = {
                content: content,
                timestamp: Date.now(),
                quality: content.quality
            };

            this.extractionCache.set(cacheKey, cacheData);

            // 清理过期缓存
            this.cleanupCache();

        } catch (error) {
            contentLogger.error('缓存提取结果失败', { error: error.message });
        }
    }

    /**
     * @function cleanupCache - 清理过期缓存
     */
    cleanupCache() {
        try {
            const now = Date.now();
            const ttl = AISP_CONFIG.ADVANCED_EXTRACTION.PERFORMANCE.CACHE_TTL;

            for (const [key, data] of this.extractionCache.entries()) {
                if (now - data.timestamp > ttl) {
                    this.extractionCache.delete(key);
                }
            }
        } catch (error) {
            // 忽略清理错误
        }
    }

    /**
     * @function extractStandardContent - 提取标准内容
     * @returns {Promise<Object>} 标准内容
     */
    async extractStandardContent() {
        try {
            return {
                text: aisp_extractText(),
                images: aisp_extractImages(),
                structured: aisp_extractStructuredContent(),
                metadata: aisp_extractMetadata()
            };
        } catch (error) {
            contentLogger.error('标准内容提取失败', { error: error.message });
            return { text: '', images: [], structured: {}, metadata: {} };
        }
    }

    /**
     * @function sleep - 异步延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // #endregion
}

// 创建高级内容提取器实例
const advancedExtractor = new AdvancedContentExtractor();

/**
 * @function aisp_initializeAdvancedExtraction - 初始化高级内容提取
 * @description 在内容脚本初始化时调用
 */
async function aisp_initializeAdvancedExtraction() {
    try {
        await advancedExtractor.initialize();
        contentLogger.info('高级内容提取功能已启用');
    } catch (error) {
        contentLogger.error('高级内容提取初始化失败', { error: error.message });
    }
}

/**
 * @function aisp_performAdvancedExtraction - 执行高级内容提取
 * @description 对外提供的高级提取接口
 * @returns {Promise<Object>} 提取结果
 */
async function aisp_performAdvancedExtraction() {
    try {
        return await advancedExtractor.extractAdvancedContent();
    } catch (error) {
        contentLogger.error('高级内容提取执行失败', { error: error.message });
        return null;
    }
}

// #endregion
