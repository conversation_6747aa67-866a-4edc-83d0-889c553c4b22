/**
 * @file AI Side Panel 快捷回复模板弹窗组件
 * @description 实现输入框上方的模板弹窗，支持模板搜索、预览、快速插入等功能
 */

// #region 模板弹窗组件类
/**
 * @class TemplatePopup - 模板弹窗组件
 * @description 管理快捷回复模板的弹窗显示和交互
 */
class TemplatePopup {
    /**
     * @function constructor - 构造函数
     * @description 初始化模板弹窗组件实例
     * @param {string|HTMLElement} inputElement - 关联的输入框元素或选择器
     * @param {Object} options - 配置选项对象
     */
    constructor(inputElement, options = {}) {
        this.inputElement = typeof inputElement === 'string' 
            ? document.querySelector(inputElement) 
            : inputElement;
            
        if (!this.inputElement) {
            throw new Error('模板弹窗关联的输入框元素未找到');
        }
        
        this.options = {
            maxTemplates: 10,           // 最大显示模板数量
            showPreview: true,          // 是否显示预览
            enableSearch: true,         // 是否启用搜索
            enableKeyboard: true,       // 是否启用键盘导航
            autoShow: true,             // 是否自动显示
            position: 'top',            // 弹窗位置 ('top', 'bottom')
            language: 'zh_CN',          // 当前语言
            onTemplateSelect: null,     // 模板选择回调
            onTemplateInsert: null,     // 模板插入回调
            ...options
        };
        
        this.templateManager = null;
        this.popup = null;
        this.isVisible = false;
        this.currentTemplates = [];
        this.selectedIndex = -1;
        this.searchQuery = '';
        this.isInitialized = false;

        // 光标预测输入相关属性
        this.predictiveInput = {
            enabled: true,
            currentPrediction: '',
            predictions: [],
            selectedPredictionIndex: -1,
            inputHistory: [],
            maxHistorySize: 100
        };

        this.initialize();
    }
    
    /**
     * @function initialize - 初始化组件
     * @description 初始化模板弹窗组件，包括模板管理器、DOM结构和事件绑定
     * @returns {Promise<void>} 初始化完成的Promise
     */
    async initialize() {
        try {
            // 获取模板管理器
            if (typeof getTemplateManager === 'function') {
                this.templateManager = getTemplateManager();
                await this.templateManager.initialize();
            } else {
                console.warn('⚠️ 模板管理器不可用');
                return;
            }
            
            // 创建弹窗元素
            this.createPopup();
            
            // 添加样式
            this.addStyles();
            
            // 绑定事件
            this.bindEvents();
            
            this.isInitialized = true;
            console.log('✅ 模板弹窗组件初始化完成');
            
        } catch (error) {
            console.error('❌ 模板弹窗组件初始化失败:', error);
        }
    }
    
    /**
     * @function createPopup - 创建弹窗元素
     * @description 创建模板弹窗的DOM结构，包括搜索框、模板列表和预览区域
     * @returns {void}
     */
    createPopup() {
        this.popup = document.createElement('div');
        this.popup.className = 'template-popup';
        this.popup.style.display = 'none';
        
        this.popup.innerHTML = `
            <div class="template-popup-header">
                <div class="template-search-container">
                    <input type="text" 
                           class="template-search-input" 
                           placeholder="搜索模板..." 
                           autocomplete="off">
                    <div class="template-search-icon">🔍</div>
                </div>
                <div class="template-popup-actions">
                    <button class="template-manage-btn" title="管理模板">⚙️</button>
                    <button class="template-close-btn" title="关闭">✕</button>
                </div>
            </div>
            <div class="template-popup-content">
                <div class="template-list"></div>
                <div class="template-preview" style="display: none;">
                    <div class="template-preview-header">预览</div>
                    <div class="template-preview-content"></div>
                </div>
            </div>
            <div class="template-popup-footer">
                <div class="template-hint">
                    <span class="template-hint-key">Tab</span> 插入模板
                    <span class="template-hint-key">↑↓</span> 选择
                    <span class="template-hint-key">Esc</span> 关闭
                </div>
            </div>
        `;
        
        // 插入到输入框附近
        this.inputElement.parentNode.insertBefore(this.popup, this.inputElement);
        
        // 设置初始位置
        this.updatePosition();
    }
    
    /**
     * @function addStyles - 添加样式
     * @description 添加模板弹窗所需的CSS样式，包括Apple Design风格样式
     * @returns {void}
     */
    addStyles() {
        const styleId = 'template-popup-styles';
        if (document.getElementById(styleId)) return;
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .template-popup {
                position: absolute;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                border-radius: 12px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                z-index: 10000;
                min-width: 320px;
                max-width: 480px;
                max-height: 400px;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
                transition: all 0.3s ease;
                opacity: 0;
                transform: translateY(-10px);
            }
            
            .template-popup.visible {
                opacity: 1;
                transform: translateY(0);
            }
            
            .template-popup-header {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                background: rgba(0, 122, 255, 0.05);
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .template-search-container {
                flex: 1;
                position: relative;
                display: flex;
                align-items: center;
            }
            
            .template-search-input {
                width: 100%;
                padding: 8px 12px 8px 36px;
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                outline: none;
                transition: all 0.2s ease;
            }
            
            .template-search-input:focus {
                border-color: #007aff;
                background: rgba(255, 255, 255, 1);
            }
            
            .template-search-icon {
                position: absolute;
                left: 12px;
                color: #86868b;
                font-size: 14px;
                pointer-events: none;
            }
            
            .template-popup-actions {
                display: flex;
                gap: 8px;
                margin-left: 12px;
            }
            
            .template-manage-btn,
            .template-close-btn {
                background: none;
                border: none;
                padding: 8px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                color: #86868b;
                transition: all 0.2s ease;
            }
            
            .template-manage-btn:hover,
            .template-close-btn:hover {
                background: rgba(0, 122, 255, 0.1);
                color: #007aff;
            }
            
            .template-popup-content {
                display: flex;
                max-height: 280px;
            }
            
            .template-list {
                flex: 1;
                overflow-y: auto;
                padding: 8px 0;
            }
            
            .template-item {
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease;
                position: relative;
            }
            
            .template-item:last-child {
                border-bottom: none;
            }
            
            .template-item:hover,
            .template-item.selected {
                background: rgba(0, 122, 255, 0.1);
            }
            
            .template-item.selected::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: #007aff;
            }
            
            .template-title {
                font-size: 14px;
                font-weight: 600;
                color: #1d1d1f;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .template-category {
                display: inline-block;
                background: #007aff;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: 500;
            }
            
            .template-content-preview {
                font-size: 12px;
                color: #86868b;
                line-height: 1.4;
                max-height: 32px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            
            .template-preview {
                width: 200px;
                border-left: 1px solid rgba(0, 0, 0, 0.05);
                background: rgba(248, 248, 248, 0.8);
            }
            
            .template-preview-header {
                padding: 12px 16px;
                font-size: 12px;
                font-weight: 600;
                color: #86868b;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .template-preview-content {
                padding: 16px;
                font-size: 13px;
                line-height: 1.5;
                color: #1d1d1f;
                white-space: pre-wrap;
                word-wrap: break-word;
            }
            
            .template-popup-footer {
                padding: 8px 16px;
                background: rgba(248, 248, 248, 0.8);
                border-top: 1px solid rgba(0, 0, 0, 0.05);
            }
            
            .template-hint {
                font-size: 11px;
                color: #86868b;
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .template-hint-key {
                background: rgba(0, 0, 0, 0.1);
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'SF Mono', Monaco, monospace;
                font-size: 10px;
            }
            
            .template-empty {
                padding: 32px 16px;
                text-align: center;
                color: #86868b;
            }
            
            .template-empty-icon {
                font-size: 32px;
                margin-bottom: 12px;
            }
            
            .template-empty-text {
                font-size: 14px;
                line-height: 1.5;
            }
            
            .template-loading {
                padding: 32px 16px;
                text-align: center;
                color: #86868b;
            }
            
            .template-loading-spinner {
                width: 20px;
                height: 20px;
                border: 2px solid #e5e5e7;
                border-top: 2px solid #007aff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 12px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* 滚动条样式 */
            .template-list::-webkit-scrollbar {
                width: 6px;
            }
            
            .template-list::-webkit-scrollbar-track {
                background: transparent;
            }
            
            .template-list::-webkit-scrollbar-thumb {
                background: rgba(0, 0, 0, 0.2);
                border-radius: 3px;
            }
            
            .template-list::-webkit-scrollbar-thumb:hover {
                background: rgba(0, 0, 0, 0.3);
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * @function bindEvents - 绑定事件
     * @description 绑定输入框和弹窗的各种事件，包括键盘导航和鼠标交互
     * @returns {void}
     */
    bindEvents() {
        // 输入框事件
        this.inputElement.addEventListener('focus', () => this.handleInputFocus());
        this.inputElement.addEventListener('blur', (e) => this.handleInputBlur(e));
        this.inputElement.addEventListener('input', (e) => this.handleInputChange(e));
        this.inputElement.addEventListener('keydown', (e) => this.handleInputKeydown(e));

        // 搜索框事件
        const searchInput = this.popup.querySelector('.template-search-input');
        searchInput.addEventListener('input', (e) => this.handleSearchInput(e));
        searchInput.addEventListener('keydown', (e) => this.handleSearchKeydown(e));

        // 按钮事件
        const manageBtn = this.popup.querySelector('.template-manage-btn');
        const closeBtn = this.popup.querySelector('.template-close-btn');

        manageBtn.addEventListener('click', () => this.openTemplateManager());
        closeBtn.addEventListener('click', () => this.hide());

        // 模板列表事件
        const templateList = this.popup.querySelector('.template-list');
        templateList.addEventListener('click', (e) => this.handleTemplateClick(e));
        templateList.addEventListener('mouseover', (e) => this.handleTemplateHover(e));

        // 全局事件
        document.addEventListener('click', (e) => this.handleDocumentClick(e));
        window.addEventListener('resize', () => this.updatePosition());
    }

    /**
     * @function handleInputFocus - 处理输入框获得焦点
     * @description 输入框获得焦点时的处理逻辑，自动显示模板弹窗
     * @returns {void}
     */
    handleInputFocus() {
        if (this.options.autoShow && this.isInitialized) {
            this.show();
        }
    }

    /**
     * @function handleInputBlur - 处理输入框失去焦点
     * @description 输入框失去焦点时的处理逻辑，延迟隐藏弹窗以允许点击操作
     * @param {Event} event - 失去焦点事件对象
     * @returns {void}
     */
    handleInputBlur(event) {
        // 清除预测提示
        this.aisp_clearPrediction();

        // 延迟隐藏，允许点击弹窗内容
        setTimeout(() => {
            if (!this.popup.contains(document.activeElement)) {
                this.hide();
            }
        }, 150);
    }

    /**
     * @function handleInputChange - 处理输入框内容变化
     * @description 输入框内容变化时的处理逻辑，触发智能搜索和预测
     * @param {Event} event - 输入变化事件对象
     * @returns {void}
     */
    handleInputChange(event) {
        const value = event.target.value;

        // 触发预测输入
        if (this.predictiveInput.enabled) {
            this.aisp_predictInput(value);
        }

        // 智能预测补全
        if (this.options.enableSearch && value.length > 0) {
            this.searchTemplates(value);
        } else {
            this.loadTemplates();
        }
    }

    /**
     * @function handleInputKeydown - 处理输入框键盘事件
     * @description 处理输入框的键盘导航和快捷键，包括Tab插入、方向键选择等
     * @param {KeyboardEvent} event - 键盘事件对象
     * @returns {void}
     */
    handleInputKeydown(event) {
        if (!this.isVisible) return;

        switch (event.key) {
            case 'Tab':
                event.preventDefault();
                // 优先处理预测输入
                if (this.predictiveInput.currentPrediction) {
                    this.aisp_confirmPrediction();
                } else {
                    this.insertSelectedTemplate();
                }
                break;

            case 'ArrowUp':
                event.preventDefault();
                // 如果有预测选项，优先处理预测选项切换
                if (this.predictiveInput.predictions.length > 0) {
                    this.aisp_cyclePredictions('up');
                } else {
                    this.selectPreviousTemplate();
                }
                break;

            case 'ArrowDown':
                event.preventDefault();
                // 如果有预测选项，优先处理预测选项切换
                if (this.predictiveInput.predictions.length > 0) {
                    this.aisp_cyclePredictions('down');
                } else {
                    this.selectNextTemplate();
                }
                break;

            case 'Escape':
                event.preventDefault();
                // 优先清除预测，然后隐藏弹窗
                if (this.predictiveInput.currentPrediction) {
                    this.aisp_clearPrediction();
                } else {
                    this.hide();
                }
                break;

            case 'Enter':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.insertSelectedTemplate();
                }
                break;
        }
    }

    /**
     * @function handleSearchInput - 处理搜索输入
     * @description 处理搜索框的输入事件
     * @param {Event} event - 输入事件
     */
    handleSearchInput(event) {
        const query = event.target.value;
        this.searchQuery = query;
        this.searchTemplates(query);
    }

    /**
     * @function handleSearchKeydown - 处理搜索框键盘事件
     * @description 处理搜索框的键盘事件
     * @param {Event} event - 键盘事件
     */
    handleSearchKeydown(event) {
        switch (event.key) {
            case 'ArrowUp':
            case 'ArrowDown':
            case 'Tab':
            case 'Enter':
                event.preventDefault();
                // 将焦点转移到输入框并处理键盘事件
                this.inputElement.focus();
                this.handleInputKeydown(event);
                break;

            case 'Escape':
                event.preventDefault();
                this.hide();
                break;
        }
    }

    /**
     * @function handleTemplateClick - 处理模板点击
     * @description 处理模板项的点击事件
     * @param {Event} event - 点击事件
     */
    handleTemplateClick(event) {
        const templateItem = event.target.closest('.template-item');
        if (!templateItem) return;

        const templateId = templateItem.getAttribute('data-template-id');
        const template = this.currentTemplates.find(t => t.id === templateId);

        if (template) {
            this.selectTemplate(template);
            this.insertTemplate(template);
        }
    }

    /**
     * @function handleTemplateHover - 处理模板悬浮
     * @description 处理模板项的悬浮事件
     * @param {Event} event - 悬浮事件
     */
    handleTemplateHover(event) {
        const templateItem = event.target.closest('.template-item');
        if (!templateItem) return;

        const templateId = templateItem.getAttribute('data-template-id');
        const template = this.currentTemplates.find(t => t.id === templateId);

        if (template) {
            this.selectTemplateById(templateId);
            this.showPreview(template);
        }
    }

    /**
     * @function handleDocumentClick - 处理文档点击
     * @description 处理文档的点击事件，用于关闭弹窗
     * @param {Event} event - 点击事件
     */
    handleDocumentClick(event) {
        if (!this.isVisible) return;

        // 如果点击在弹窗或输入框外，关闭弹窗
        if (!this.popup.contains(event.target) &&
            !this.inputElement.contains(event.target)) {
            this.hide();
        }
    }

    /**
     * @function show - 显示弹窗
     * @description 显示模板弹窗
     */
    async show() {
        if (!this.isInitialized || this.isVisible) return;

        try {
            // 加载模板
            await this.loadTemplates();

            // 更新位置
            this.updatePosition();

            // 显示弹窗
            this.popup.style.display = 'block';

            // 添加显示动画
            requestAnimationFrame(() => {
                this.popup.classList.add('visible');
            });

            this.isVisible = true;

            // 清空搜索
            const searchInput = this.popup.querySelector('.template-search-input');
            searchInput.value = '';
            this.searchQuery = '';

            console.log('📋 模板弹窗已显示');

        } catch (error) {
            console.error('显示模板弹窗失败:', error);
        }
    }

    /**
     * @function hide - 隐藏弹窗
     * @description 隐藏模板弹窗
     */
    hide() {
        if (!this.isVisible) return;

        // 移除显示动画
        this.popup.classList.remove('visible');

        // 延迟隐藏
        setTimeout(() => {
            this.popup.style.display = 'none';
        }, 300);

        this.isVisible = false;
        this.selectedIndex = -1;

        // 隐藏预览
        this.hidePreview();

        console.log('📋 模板弹窗已隐藏');
    }

    /**
     * @function loadTemplates - 加载模板
     * @description 从模板管理器加载模板数据
     */
    async loadTemplates() {
        if (!this.templateManager) return;

        try {
            const templateList = this.popup.querySelector('.template-list');
            templateList.innerHTML = '<div class="template-loading"><div class="template-loading-spinner"></div>加载模板中...</div>';

            // 获取模板
            const templates = await this.templateManager.getTemplates({
                language: this.options.language,
                limit: this.options.maxTemplates,
                active: true
            });

            this.currentTemplates = templates;
            this.displayTemplates(templates);

        } catch (error) {
            console.error('加载模板失败:', error);
            this.showError('加载模板失败');
        }
    }

    /**
     * @function searchTemplates - 搜索模板
     * @description 根据查询条件搜索模板
     * @param {string} query - 搜索查询
     */
    async searchTemplates(query) {
        if (!this.templateManager || !query.trim()) {
            await this.loadTemplates();
            return;
        }

        try {
            const templates = await this.templateManager.searchTemplates(query, {
                language: this.options.language,
                limit: this.options.maxTemplates
            });

            this.currentTemplates = templates;
            this.displayTemplates(templates);

        } catch (error) {
            console.error('搜索模板失败:', error);
            this.showError('搜索模板失败');
        }
    }

    /**
     * @function displayTemplates - 显示模板列表
     * @description 在弹窗中显示模板列表
     * @param {Array} templates - 模板数组
     */
    displayTemplates(templates) {
        const templateList = this.popup.querySelector('.template-list');

        if (!templates || templates.length === 0) {
            templateList.innerHTML = `
                <div class="template-empty">
                    <div class="template-empty-icon">📝</div>
                    <div class="template-empty-text">
                        ${this.searchQuery ? '未找到匹配的模板' : '暂无可用模板'}
                    </div>
                </div>
            `;
            return;
        }

        const templatesHTML = templates.map((template, index) => {
            const categoryColor = this.getCategoryColor(template.category);

            return `
                <div class="template-item"
                     data-template-id="${template.id}"
                     data-index="${index}">
                    <div class="template-title">
                        ${this.escapeHtml(template.title)}
                        ${template.category ? `
                            <span class="template-category" style="background-color: ${categoryColor}">
                                ${this.escapeHtml(template.category)}
                            </span>
                        ` : ''}
                    </div>
                    <div class="template-content-preview">
                        ${this.escapeHtml(template.content.substring(0, 100))}${template.content.length > 100 ? '...' : ''}
                    </div>
                </div>
            `;
        }).join('');

        templateList.innerHTML = templatesHTML;

        // 默认选择第一个模板
        if (templates.length > 0) {
            this.selectedIndex = 0;
            this.updateSelection();
            this.showPreview(templates[0]);
        }
    }

    /**
     * @function selectTemplate - 选择模板
     * @description 选择指定的模板
     * @param {Object} template - 模板对象
     */
    selectTemplate(template) {
        const index = this.currentTemplates.findIndex(t => t.id === template.id);
        if (index !== -1) {
            this.selectedIndex = index;
            this.updateSelection();
            this.showPreview(template);

            // 触发选择回调
            if (this.options.onTemplateSelect && typeof this.options.onTemplateSelect === 'function') {
                this.options.onTemplateSelect(template);
            }
        }
    }

    /**
     * @function selectTemplateById - 根据ID选择模板
     * @description 根据模板ID选择模板
     * @param {string} templateId - 模板ID
     */
    selectTemplateById(templateId) {
        const template = this.currentTemplates.find(t => t.id === templateId);
        if (template) {
            this.selectTemplate(template);
        }
    }

    /**
     * @function selectNextTemplate - 选择下一个模板
     * @description 选择列表中的下一个模板
     */
    selectNextTemplate() {
        if (this.currentTemplates.length === 0) return;

        this.selectedIndex = (this.selectedIndex + 1) % this.currentTemplates.length;
        this.updateSelection();
        this.showPreview(this.currentTemplates[this.selectedIndex]);
    }

    /**
     * @function selectPreviousTemplate - 选择上一个模板
     * @description 选择列表中的上一个模板
     */
    selectPreviousTemplate() {
        if (this.currentTemplates.length === 0) return;

        this.selectedIndex = this.selectedIndex <= 0
            ? this.currentTemplates.length - 1
            : this.selectedIndex - 1;
        this.updateSelection();
        this.showPreview(this.currentTemplates[this.selectedIndex]);
    }

    /**
     * @function updateSelection - 更新选择状态
     * @description 更新模板列表的选择状态显示
     */
    updateSelection() {
        const templateItems = this.popup.querySelectorAll('.template-item');

        templateItems.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('selected');
                // 滚动到可见区域
                item.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
            } else {
                item.classList.remove('selected');
            }
        });
    }

    /**
     * @function insertSelectedTemplate - 插入选中的模板
     * @description 插入当前选中的模板到输入框
     */
    insertSelectedTemplate() {
        if (this.selectedIndex >= 0 && this.currentTemplates[this.selectedIndex]) {
            const template = this.currentTemplates[this.selectedIndex];
            this.insertTemplate(template);
        }
    }

    /**
     * @function insertTemplate - 插入模板
     * @description 将模板内容插入到输入框
     * @param {Object} template - 模板对象
     */
    insertTemplate(template) {
        if (!template || !this.inputElement) return;

        try {
            // 获取当前光标位置
            const cursorPosition = this.inputElement.selectionStart || 0;
            const currentValue = this.inputElement.value;

            // 插入模板内容
            const beforeCursor = currentValue.substring(0, cursorPosition);
            const afterCursor = currentValue.substring(this.inputElement.selectionEnd || cursorPosition);
            const newValue = beforeCursor + template.content + afterCursor;

            // 更新输入框内容
            this.inputElement.value = newValue;

            // 设置新的光标位置
            const newCursorPosition = cursorPosition + template.content.length;
            this.inputElement.setSelectionRange(newCursorPosition, newCursorPosition);

            // 触发input事件
            this.inputElement.dispatchEvent(new Event('input', { bubbles: true }));

            // 隐藏弹窗
            this.hide();

            // 聚焦输入框
            this.inputElement.focus();

            // 触发插入回调
            if (this.options.onTemplateInsert && typeof this.options.onTemplateInsert === 'function') {
                this.options.onTemplateInsert(template);
            }

            console.log('📋 模板已插入:', template.title);

        } catch (error) {
            console.error('插入模板失败:', error);
        }
    }

    /**
     * @function showPreview - 显示模板预览
     * @description 在预览区域显示模板内容
     * @param {Object} template - 模板对象
     */
    showPreview(template) {
        if (!this.options.showPreview || !template) return;

        const preview = this.popup.querySelector('.template-preview');
        const previewContent = this.popup.querySelector('.template-preview-content');

        if (preview && previewContent) {
            previewContent.textContent = template.content;
            preview.style.display = 'block';
        }
    }

    /**
     * @function hidePreview - 隐藏模板预览
     * @description 隐藏模板预览区域
     */
    hidePreview() {
        const preview = this.popup.querySelector('.template-preview');
        if (preview) {
            preview.style.display = 'none';
        }
    }

    /**
     * @function updatePosition - 更新弹窗位置
     * @description 根据输入框位置更新弹窗位置
     */
    updatePosition() {
        if (!this.inputElement || !this.popup) return;

        const inputRect = this.inputElement.getBoundingClientRect();
        const popupRect = this.popup.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        let top, left;

        if (this.options.position === 'bottom' ||
            (this.options.position === 'top' && inputRect.top < popupRect.height + 20)) {
            // 显示在输入框下方
            top = inputRect.bottom + 8;
        } else {
            // 显示在输入框上方
            top = inputRect.top - popupRect.height - 8;
        }

        // 水平居中对齐
        left = inputRect.left + (inputRect.width - popupRect.width) / 2;

        // 确保不超出视口
        if (left < 10) left = 10;
        if (left + popupRect.width > viewportWidth - 10) {
            left = viewportWidth - popupRect.width - 10;
        }

        if (top < 10) top = 10;
        if (top + popupRect.height > viewportHeight - 10) {
            top = viewportHeight - popupRect.height - 10;
        }

        this.popup.style.left = left + 'px';
        this.popup.style.top = top + 'px';
    }

    /**
     * @function openTemplateManager - 打开模板管理器
     * @description 打开模板管理界面
     */
    openTemplateManager() {
        // 这里可以打开模板管理界面
        console.log('打开模板管理器');

        // 暂时显示开发中提示
        alert('模板管理功能正在开发中...');
    }

    /**
     * @function showError - 显示错误信息
     * @description 在模板列表中显示错误信息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const templateList = this.popup.querySelector('.template-list');
        templateList.innerHTML = `
            <div class="template-empty">
                <div class="template-empty-icon">❌</div>
                <div class="template-empty-text">${this.escapeHtml(message)}</div>
            </div>
        `;
    }

    /**
     * @function getCategoryColor - 获取分类颜色
     * @description 根据分类名称获取对应的颜色
     * @param {string} category - 分类名称
     * @returns {string} 颜色值
     */
    getCategoryColor(category) {
        const colors = {
            '问候': '#34c759',
            '感谢': '#007aff',
            '道歉': '#ff9500',
            '询问': '#5856d6',
            '确认': '#00c7be',
            '结束': '#8e8e93'
        };

        return colors[category] || '#007aff';
    }

    /**
     * @function escapeHtml - 转义HTML
     * @description 转义HTML特殊字符
     * @param {string} text - 原始文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * @function updateLanguage - 更新语言
     * @description 更新组件的显示语言
     * @param {string} language - 新的语言代码
     */
    updateLanguage(language) {
        this.options.language = language;

        // 重新加载模板
        if (this.isVisible) {
            this.loadTemplates();
        }
    }

    /**
     * @function destroy - 销毁组件
     * @description 清理组件资源
     */
    destroy() {
        if (this.popup && this.popup.parentNode) {
            this.popup.parentNode.removeChild(this.popup);
        }

        this.popup = null;
        this.templateManager = null;
        this.currentTemplates = [];
        this.isInitialized = false;
    }

    // #region 光标预测输入功能
    /**
     * @function aisp_predictInput - 智能预测输入
     * @description 基于历史输入和模板内容预测用户可能的输入
     * @param {string} currentInput - 当前输入内容
     * @returns {void}
     */
    aisp_predictInput(currentInput) {
        if (!this.predictiveInput.enabled || currentInput.length < 2) {
            this.aisp_clearPrediction();
            return;
        }

        try {
            // 生成预测选项
            const predictions = this.aisp_generatePredictions(currentInput);

            if (predictions.length > 0) {
                this.predictiveInput.predictions = predictions;
                this.predictiveInput.selectedPredictionIndex = 0;
                this.predictiveInput.currentPrediction = predictions[0];

                // 显示预测提示
                this.aisp_showPredictionHint(predictions[0], currentInput);
            } else {
                this.aisp_clearPrediction();
            }

        } catch (error) {
            console.warn('⚠️ 预测输入失败:', error);
            this.aisp_clearPrediction();
        }
    }

    /**
     * @function aisp_generatePredictions - 生成预测选项
     * @description 基于多种策略生成输入预测选项
     * @param {string} input - 当前输入
     * @returns {Array<string>} 预测选项数组
     */
    aisp_generatePredictions(input) {
        const predictions = new Set();
        const inputLower = input.toLowerCase();

        // 策略1: 基于模板内容匹配
        this.currentTemplates.forEach(template => {
            const content = template.content.toLowerCase();
            if (content.includes(inputLower)) {
                // 提取包含输入的句子
                const sentences = template.content.split(/[。！？.!?]/);
                sentences.forEach(sentence => {
                    if (sentence.toLowerCase().includes(inputLower) && sentence.trim().length > input.length) {
                        predictions.add(sentence.trim());
                    }
                });
            }
        });

        // 策略2: 基于历史输入匹配
        this.predictiveInput.inputHistory.forEach(historyItem => {
            if (historyItem.toLowerCase().startsWith(inputLower) && historyItem.length > input.length) {
                predictions.add(historyItem);
            }
        });

        // 策略3: 基于常用短语补全
        const commonPhrases = this.aisp_getCommonPhrases(inputLower);
        commonPhrases.forEach(phrase => {
            if (phrase.toLowerCase().startsWith(inputLower)) {
                predictions.add(phrase);
            }
        });

        // 转换为数组并按相关性排序
        return Array.from(predictions)
            .slice(0, 5) // 最多5个预测
            .sort((a, b) => {
                // 优先显示更短的、更相关的预测
                const aScore = this.aisp_calculatePredictionScore(a, input);
                const bScore = this.aisp_calculatePredictionScore(b, input);
                return bScore - aScore;
            });
    }

    /**
     * @function aisp_getCommonPhrases - 获取常用短语
     * @description 根据输入前缀获取常用短语
     * @param {string} prefix - 输入前缀
     * @returns {Array<string>} 常用短语数组
     */
    aisp_getCommonPhrases(prefix) {
        const commonPhrases = {
            '你好': ['你好，很高兴为您服务', '你好，有什么可以帮助您的吗'],
            '谢谢': ['谢谢您的反馈', '谢谢您的耐心等待', '谢谢您的理解'],
            '抱歉': ['抱歉给您带来不便', '抱歉让您久等了', '抱歉，这个问题我需要进一步确认'],
            '请问': ['请问您需要什么帮助', '请问还有其他问题吗', '请问您方便提供更多信息吗'],
            '关于': ['关于这个问题', '关于您的需求', '关于具体的实施方案'],
            '如果': ['如果您有任何问题', '如果需要进一步帮助', '如果您不满意我们的服务']
        };

        const matches = [];
        for (const [key, phrases] of Object.entries(commonPhrases)) {
            if (key.startsWith(prefix)) {
                matches.push(...phrases);
            }
        }

        return matches;
    }

    /**
     * @function aisp_calculatePredictionScore - 计算预测分数
     * @description 计算预测选项的相关性分数
     * @param {string} prediction - 预测文本
     * @param {string} input - 输入文本
     * @returns {number} 相关性分数
     */
    aisp_calculatePredictionScore(prediction, input) {
        let score = 0;

        // 长度相似性（较短的预测得分更高）
        const lengthRatio = input.length / prediction.length;
        score += lengthRatio * 10;

        // 前缀匹配度
        if (prediction.toLowerCase().startsWith(input.toLowerCase())) {
            score += 20;
        }

        // 包含度
        if (prediction.toLowerCase().includes(input.toLowerCase())) {
            score += 10;
        }

        // 历史使用频率（如果在历史中出现过）
        if (this.predictiveInput.inputHistory.includes(prediction)) {
            score += 15;
        }

        return score;
    }

    /**
     * @function aisp_showPredictionHint - 显示预测提示
     * @description 在输入框中显示预测提示
     * @param {string} prediction - 预测文本
     * @param {string} currentInput - 当前输入
     * @returns {void}
     */
    aisp_showPredictionHint(prediction, currentInput) {
        // 创建预测提示元素
        let hintElement = this.inputElement.parentNode.querySelector('.prediction-hint');

        if (!hintElement) {
            hintElement = document.createElement('div');
            hintElement.className = 'prediction-hint';
            hintElement.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;
                font-family: inherit;
                font-size: inherit;
                padding: inherit;
                margin: inherit;
                border: inherit;
                background: transparent;
                color: rgba(0, 0, 0, 0.4);
                z-index: 1;
                white-space: pre-wrap;
                overflow: hidden;
            `;

            // 确保输入框有相对定位
            if (getComputedStyle(this.inputElement.parentNode).position === 'static') {
                this.inputElement.parentNode.style.position = 'relative';
            }

            this.inputElement.parentNode.appendChild(hintElement);
        }

        // 显示预测提示（灰色显示未输入的部分）
        const remainingText = prediction.substring(currentInput.length);
        hintElement.textContent = currentInput + remainingText;
        hintElement.style.display = 'block';

        // 添加预测选项指示器
        this.aisp_showPredictionOptions();
    }

    /**
     * @function aisp_showPredictionOptions - 显示预测选项
     * @description 显示可选的预测选项列表
     * @returns {void}
     */
    aisp_showPredictionOptions() {
        if (this.predictiveInput.predictions.length <= 1) return;

        let optionsElement = document.querySelector('.prediction-options');

        if (!optionsElement) {
            optionsElement = document.createElement('div');
            optionsElement.className = 'prediction-options';
            optionsElement.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                z-index: 10001;
                max-height: 150px;
                overflow-y: auto;
                font-size: 12px;
                margin-top: 4px;
            `;

            this.inputElement.parentNode.appendChild(optionsElement);
        }

        // 生成选项列表
        const optionsHtml = this.predictiveInput.predictions.map((prediction, index) => {
            const isSelected = index === this.predictiveInput.selectedPredictionIndex;
            return `
                <div class="prediction-option ${isSelected ? 'selected' : ''}"
                     data-index="${index}"
                     style="
                         padding: 8px 12px;
                         cursor: pointer;
                         border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                         background: ${isSelected ? 'rgba(0, 122, 255, 0.1)' : 'transparent'};
                         transition: background 0.2s ease;
                     ">
                    ${this.escapeHtml(prediction)}
                </div>
            `;
        }).join('');

        optionsElement.innerHTML = optionsHtml;
        optionsElement.style.display = 'block';

        // 绑定点击事件
        optionsElement.addEventListener('click', (e) => {
            const optionElement = e.target.closest('.prediction-option');
            if (optionElement) {
                const index = parseInt(optionElement.dataset.index);
                this.predictiveInput.selectedPredictionIndex = index;
                this.predictiveInput.currentPrediction = this.predictiveInput.predictions[index];
                this.aisp_confirmPrediction();
            }
        });
    }

    /**
     * @function aisp_confirmPrediction - 确认预测输入
     * @description 确认并应用当前的预测输入
     * @returns {void}
     */
    aisp_confirmPrediction() {
        if (!this.predictiveInput.currentPrediction) return;

        // 设置输入框值
        this.inputElement.value = this.predictiveInput.currentPrediction;

        // 添加到历史记录
        this.aisp_addToInputHistory(this.predictiveInput.currentPrediction);

        // 清除预测提示
        this.aisp_clearPrediction();

        // 触发输入事件
        const event = new Event('input', { bubbles: true });
        this.inputElement.dispatchEvent(event);

        // 设置光标位置到末尾
        this.inputElement.setSelectionRange(this.inputElement.value.length, this.inputElement.value.length);
    }

    /**
     * @function aisp_clearPrediction - 清除预测提示
     * @description 清除所有预测相关的UI元素
     * @returns {void}
     */
    aisp_clearPrediction() {
        // 清除预测状态
        this.predictiveInput.currentPrediction = '';
        this.predictiveInput.predictions = [];
        this.predictiveInput.selectedPredictionIndex = -1;

        // 清除预测提示元素
        const hintElement = this.inputElement.parentNode.querySelector('.prediction-hint');
        if (hintElement) {
            hintElement.style.display = 'none';
        }

        // 清除预测选项元素
        const optionsElement = document.querySelector('.prediction-options');
        if (optionsElement) {
            optionsElement.style.display = 'none';
        }
    }

    /**
     * @function aisp_addToInputHistory - 添加到输入历史
     * @description 将输入添加到历史记录中
     * @param {string} input - 输入内容
     * @returns {void}
     */
    aisp_addToInputHistory(input) {
        if (!input || input.trim().length < 3) return;

        const trimmedInput = input.trim();

        // 移除重复项
        const existingIndex = this.predictiveInput.inputHistory.indexOf(trimmedInput);
        if (existingIndex !== -1) {
            this.predictiveInput.inputHistory.splice(existingIndex, 1);
        }

        // 添加到开头
        this.predictiveInput.inputHistory.unshift(trimmedInput);

        // 保持历史记录大小
        if (this.predictiveInput.inputHistory.length > this.predictiveInput.maxHistorySize) {
            this.predictiveInput.inputHistory = this.predictiveInput.inputHistory.slice(0, this.predictiveInput.maxHistorySize);
        }
    }

    /**
     * @function aisp_cyclePredictions - 循环切换预测选项
     * @description 使用方向键循环切换预测选项
     * @param {string} direction - 方向 ('up' 或 'down')
     * @returns {void}
     */
    aisp_cyclePredictions(direction) {
        if (this.predictiveInput.predictions.length === 0) return;

        const currentIndex = this.predictiveInput.selectedPredictionIndex;
        let newIndex;

        if (direction === 'up') {
            newIndex = currentIndex > 0 ? currentIndex - 1 : this.predictiveInput.predictions.length - 1;
        } else {
            newIndex = currentIndex < this.predictiveInput.predictions.length - 1 ? currentIndex + 1 : 0;
        }

        this.predictiveInput.selectedPredictionIndex = newIndex;
        this.predictiveInput.currentPrediction = this.predictiveInput.predictions[newIndex];

        // 更新显示
        this.aisp_showPredictionHint(this.predictiveInput.currentPrediction, this.inputElement.value);
    }
    // #endregion
}
// #endregion

console.log('📋 AI Side Panel 模板弹窗组件已加载');
